<template>
  <div ref="containerRef" style="width: 100%; height: 100%" />
  <div class="relation_box">
    <el-button type="primary" @click="showTreeGraph"> 拓扑图 </el-button>
  </div>

  <el-dialog v-model="showGraphFlag" title="首图拓扑预览" width="70vw" append-to-body>
    <div style="width: 100%; height: 650px">
      <div ref="showGraphRef" style="width: 100%; height: 100%" />
      <div style="position: absolute; right: 100px; bottom: 20px">
        <el-button type="success" @click="showGraphFlag = false">完成</el-button>
      </div>
    </div>
  </el-dialog>

  <el-dialog v-model="open" title="建立关联" width="70vw" height="80vh" append-to-body>
    <el-row gutter="20">
      <el-col :span="15">
        <div v-if="open">
          <ModelViewerCustom
            ref="modelViewerCustom"
            :objectUrl="selectModel.objectUrl"
            :currentStyle="form.style == 1 ? 'bg-woke' : 'bg-woke2'"
            :bsName="form.name"
            :open="open"
            @setPoint="setPoint"
          />
        </div>
      </el-col>
      <el-col :span="9">
        <div>
          <el-form ref="modelRef" :model="form" :rules="rules" label-width="100px">
            <el-form-item label="标识牌样式" prop="factoryCode">
              <el-radio-group v-model="form.style" class="ml-4">
                <el-radio label="1" size="large" :selected="form.style == 1">样式 1</el-radio>
                <el-radio label="2" size="large" :selected="form.style == 2">样式 2</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="标识牌名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入模型名称" />
            </el-form-item>
            <el-form-item label="标识牌位置">
              <div>
                <div class="flex">
                  <span>x: </span>
                  <el-input v-model="form.x" type="number" :step="0.1" placeholder="" style="margin-right: 10px" />
                </div>
                <div class="flex">
                  <span>y: </span>
                  <el-input v-model="form.y" type="number" :step="0.1" placeholder="" style="margin-right: 10px" />
                </div>
                <div class="flex">
                  <span>z: </span>
                  <el-input v-model="form.z" type="number" :step="0.1" placeholder="" style="margin-right: 10px" />
                </div>
              </div>
            </el-form-item>

            <el-form-item label="预览">
              <div v-if="form.style == 2" class="bg-woke2">
                {{ form.name }}
              </div>
              <div v-else class="bg-woke">
                {{ form.name }}
              </div>
            </el-form-item>

            <el-form-item label="跳转模型">
              <el-select
                v-model="queryParams.selectModelCode"
                placeholder="请选择模型类型"
                style="width: 100%; cursor: pointer"
                @click="handleSelectClick"
              >
                <el-option v-for="item in options || []" :key="item?.code" :label="item?.name" :value="item?.code" />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </el-col>
    </el-row>
    <template #footer>
      <div style="display: flex; justify-content: space-between">
        <div>
          <el-button v-if="form && form.id" type="danger" @click="deleteDecision()">删 除</el-button>
        </div>
        <div>
          <el-button type="primary" @click="submitDecision()">确 定</el-button>
          <el-button @click="closeDecision">取 消</el-button>
        </div>
      </div>
    </template>
  </el-dialog>

  <!-- 添加或修改业务 holo工厂3D模型对话框 -->
  <el-dialog v-model="modelOpen" title="选择未被关联模型" width="60%" append-to-body>
    <div>
      <div class="folder_xz">选择当前未被关联的模型</div>
      <el-row :gutter="20">
        <el-col
          v-for="item in modelRootList"
          :key="item.code"
          :xs="24"
          :sm="12"
          :md="8"
          :lg="6"
          style="margin-bottom: 10px"
        >
          <el-card class="model-card" shadow="hover" @click="onClickItem(item)">
            <model-viewer
              :src="'/platform-api/' + item.objectUrl"
              alt="3D 模型"
              class="model-viewer"
              :class="{ selected: item.selected }"
              auto-rotate
              camera-controls
              disable-zoom
              touch-action="pan-y"
              exposure="1.0"
              style="background-color: #182f4a; cursor: pointer"
            >
              <div v-if="!item.selected" class="model-icon" style="background: #fff; border-radius: 50%"></div>
              <svg-icon v-else class="model-icon" icon-class="select3d" />
            </model-viewer>
            <div class="card-info">
              <div class="model-name">{{ item.remark }}</div>
              <div class="model-suffix">.{{ item.suffix }}</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <pagination
        v-if="total > 0"
        v-model:page-num="queryParams.page"
        v-model:page-size="queryParams.size"
        :pageSizes="[8, 12, 16]"
        :total="total"
        @pagination="handlePaginationEventRoot"
      />

      <el-empty v-else description="暂无模型数据" :image-size="100" />
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="modelClickFun">确 定</el-button>
        <el-button @click="modelOpen = false">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { Rect as GRect, Text as GText } from '@antv/g';
import { defineEmits, onMounted, watch } from 'vue';
import * as THREE from 'three';
import { ElInput, ElMessage, ElMessageBox } from 'element-plus';
import { isStart, listModel, listModelByRelation } from '@/api/holo/model';
import {
  NodeEvent,
  Badge,
  CommonEvent,
  ExtensionCategory,
  Graph,
  GraphEvent,
  iconfont,
  Label,
  Rect,
  register,
  treeToGraphData,
} from '@antv/g6';
import Pagination from '@/components/Pagination/index.vue';
import {
  addHolo3dmodelrelation,
  delHolo3dmodelrelation,
  holo3dmodelrelationFromTopology,
  holo3dmodelrelationFromTopologyByChild,
  updateHolo3dmodelrelation,
} from '@/api/holo/holo3dmodelrelation.js';
import { delSpotlight } from '@/api/holo/spotlight.js';
/*import ModelViewerCustom from '@/components/ModelViewer/index.vue';*/
const ModelViewerCustom = defineAsyncComponent(() => import('@/components/ModelViewer/index.vue'));
//当前区域配置对象
const props = defineProps({
  selectModel: {
    type: Object,
    default: null,
  },
  modelList: {
    type: Array,
    default: [],
  },
});
const objectUrl = ref(props.selectModel.objectUrl);
const queryParams = ref({
  page: 1,
  size: 8,
  selectModelCode: null,
  isCommonList: [1, 2].join(','),
});

watch(
  () => props.selectModel,
  (value) => {
    /*  console.log(props.selectModel);*/
    objectUrl.value = props.selectModel.objectUrl;
    initGraph();
  },
  { immediate: true },
);

const total = ref(0);
const loading = ref(false);
const selectOpenModel = ref(null);
const options = ref([]);
const modelViewerCustom = ref();
const newNode = ref(null);
const fromRelationData = ref([]);

const showGraphFlag = ref();
const showGraphRef = ref();
let graphPreview = null;

function showTreeGraph() {
  if (!props.selectModel.factoryCode) {
    ElMessage.error('模型未关联工厂,无法预览关联路径');
  }
  isStart({ factoryCode: props.selectModel.factoryCode }).then((res) => {
    if (res.data) {
      //
      showGraphFlag.value = true;
      console.log();
      setTimeout(() => {
        showGraphStartPreview(res.data.code);
      }, 1000);
    } else {
      ElMessage.error('工厂尚未设置首图，无法预览关联路径');
    }
  });
}

/** 查询业务 holo工厂3D模型列表 */
function getList() {
  loading.value = true;
  if (props.selectModel.factoryCode) {
    queryParams.value.factoryCode = props.selectModel.factoryCode;
  }
  listModelByRelation(queryParams.value).then((response) => {
    modelRootList.value = response.data.list.filter((e) => e.code != props.selectModel.code);

    total.value = response.data.total;
    loading.value = false;

    if (queryParams.value.selectModelCode) {
      modelRootList.value.map((item) => {
        if (item.code == queryParams.value.selectModelCode) {
          item.selected = true;
        }
        return item;
      });
    }
  });
}

/**
 * 从图上设置位置
 * @param point
 */
function setPoint(point) {
  form.value.x = point.x;
  form.value.y = point.y;
  form.value.z = point.z;
}

/**
 * 打开模型选择
 * @param visible
 */
function handleSelectClick(visible) {
  if (visible) {
    /*console.log('select 被点击，下拉框打开');
    // 你可以在这里写打开弹窗的逻辑
    ElMessage.success('你点击了下拉框');
    return*/
    queryParams.value.page = 0;
    getList();
    modelOpen.value = true;
  }
}

/**
 * 选择模型
 * @param item
 */
function onClickItem(item) {
  modelRootList.value.map((i) => {
    i.selected = false;
  });
  item.selected = true;

  selectOpenModel.value = item;
}

/**
 * 选择完模型后 下拉框显示
 */
function modelClickFun() {
  modelOpen.value = false;
  //保存 配置
  options.value = [selectOpenModel.value];
  console.log('options.value', options.value);
  // 等 DOM 更新后再设置选中项
  nextTick(() => {
    queryParams.value.selectModelCode = selectOpenModel.value.code;
  });
}

/**
 * 分页
 * @param pagination
 */
function handlePaginationEventRoot(pagination) {
  const { pageNum, pageSize } = pagination;
  queryParams.value.page = pageNum;
  queryParams.value.size = pageSize;
  getList();
}

const emit = defineEmits(['initList']);

/**
 * 保存关联模型
 */
function submitDecision() {
  //判断更新 还是 新增
  if (form.value.id) {
    //更新
    const data = {
      id: form.value.id,
      from: form.value.from,
      factoryCode: form.value.factoryCode,
      to: queryParams.value.selectModelCode,
      data: {
        styleType: form.value.style,
        text: form.value.name,
        position: [form.value.x, form.value.y, form.value.z],
      },
    };
    data.data = JSON.stringify(data.data);
    updateHolo3dmodelrelation(data).then((res) => {
      //拓扑图展示
      initGraph();
      open.value = false;
      reset();
    });
    return;
  }
  if (fromRelationData.value.find((item) => item.to == selectOpenModel.value.code)) {
    ElMessage.error('此模型已经关联');
    return;
  }
  const data = {
    from: props.selectModel.code,
    factoryCode: props.selectModel.factoryCode,
    to: selectOpenModel.value.code,
    data: {
      styleType: form.value.style,
      text: form.value.name,
      position: [form.value.x, form.value.y, form.value.z],
    },
  };
  data.data = JSON.stringify(data.data);
  addHolo3dmodelrelation(data).then((res) => {
    //拓扑图展示
    initGraph();
    open.value = false;
    reset();
    emit('initList');
  });
}

/**
 * 关闭
 */
function closeDecision() {
  open.value = false;
  reset();
}

/**
 * 删除
 */
function deleteDecision() {
  if (!form.value.id) {
    ElMessage.error('删除出错');
  }
  ElMessageBox.confirm('是否确认删除 模型关联信息编号为"' + form.value.name + '"的数据项？')
    .then(function () {
      return delHolo3dmodelrelation(form.value.id);
    })
    .then(() => {
      //拓扑图展示
      initGraph();
      open.value = false;
      reset();
      ElMessage.success('删除成功');
    })
    .catch(() => {});
}

// 表单重置
function reset() {
  form.value = {
    style: 1,
    name: null,
    x: null,
    y: null,
    z: null,
    id: null,
  };
  queryParams.value.selectModelCode = null;
}

const style = document.createElement('style');
style.innerHTML = `@import url('${iconfont.css}');`;
document.head.appendChild(style);

const data = reactive({
  form: {
    id: null,
    style: '1', // 设置默认选中样式 1
  },
  rules: {
    groupCode: [{ required: true, message: '集团code不能为空', trigger: 'blur' }],
    organizeCode: [{ required: true, message: '组织code不能为空', trigger: 'blur' }],
    objCode: [{ required: true, message: '对象存储code不能为空', trigger: 'blur' }],
    code: [{ required: true, message: '表code不能为空', trigger: 'blur' }],
    name: [{ required: true, message: '模型名称不能为空', trigger: 'blur' }],
    factoryCode: [{ required: true, message: '工厂code不能为空', trigger: 'blur' }],
    isSystem: [{ required: true, message: '0为用户上传1为系统模型不能为空', trigger: 'blur' }],
    startStatus: [{ required: true, message: '工厂首个展示的模型0：否 1：是不能为空', trigger: 'change' }],
    isCommon: [{ required: true, message: '0用户上传1为用户配置好的公版2用户配置的非公版不能为空', trigger: 'blur' }],
    suffix: [{ required: true, message: '文件后缀不能为空', trigger: 'blur' }],
    deleteStatus: [{ required: true, message: '删除状态 0未删除 1已删除不能为空', trigger: 'change' }],
  },
});

const { form, rules } = toRefs(data);
const modelRootList = ref(false);
const modelOpen = ref(false);
const open = ref(false);
const containerRef = ref();
const COLORS = {
  B: '#1783FF',
  R: '#F46649',
  Y: '#DB9D0D',
  G: '#60C42D',
  DI: '#A7A7A7',
};

watch(
  () => [form.value.x, form.value.y, form.value.z],
  ([x, y, z]) => {
    if (modelViewerCustom.value) {
      modelViewerCustom.value.applyOffset({ x: x, y: y, z: z });
    }
  },
);

const GREY_COLOR = '#CED4D9';
let graph = null;

/**
 * 节点类
 */
class TreeNode extends Rect {
  get data() {
    return this.context.model.getNodeLikeDatum(this.id);
  }

  get childrenData() {
    return this.context.model.getChildrenData(this.id);
  }

  getLabelStyle(attributes) {
    const [width, height] = this.getSize(attributes);
    return {
      x: -width / 2,
      y: -height / 2 + 16,
      text: this.data.name,
      fontSize: 12,
      opacity: 0.85,
      fill: '#000',
      cursor: 'pointer',
    };
  }
  /**
   * 创建按钮
   * @param attributes
   * @returns {{cursor: string, textBaseline: string, textAlign: string, fill: string, backgroundHeight: number, backgroundRadius: number, x: number, y: number, fontSize: number, backgroundStroke: string, text: string, backgroundLineWidth: number, backgroundFill: string}}
   */
  getAddNodeStyle(attributes) {
    const [width, height] = this.getSize(attributes);
    return {
      backgroundFill: '#B2C2C2', // 绿色背景
      backgroundHeight: 5,
      backgroundLineWidth: 1,
      backgroundRadius: '50%',
      backgroundStroke: 'rgba(170,167,159,1)',
      cursor: 'pointer',
      fill: '#000000', // 字体白色
      fontSize: 10,
      text: '+', // 按钮显示加号
      textAlign: 'center',
      textBaseline: 'middle',
      x: width / 2 + 10, // 放在节点右边外面一点
      y: 0,
    };
  }

  /**
   * 绑定按钮和事件
   * @param attributes
   * @param container
   */
  drawAddNodeShape(attributes, container) {
    const addStyle = this.getAddNodeStyle(attributes);

    const btn = this.upsert('addNodeBtn', Badge, addStyle, container);

    if (btn && !Reflect.has(btn, '__addNodeBound__')) {
      Reflect.set(btn, '__addNodeBound__', true);
      btn.addEventListener(CommonEvent.CLICK, () => {
        const { graph } = this.context;
        // 触发自定义事件，传递当前节点ID
        graph.emit('node:add', { nodeId: this.id });
        e.stopPropagation(); // ✅ 阻止触发 node:click
      });
    }
  }

  getKeyStyle(attributes) {
    const keyStyle = super.getKeyStyle(attributes);
    return {
      ...keyStyle,
      fill: '#fff',
      lineWidth: 1,
      stroke: GREY_COLOR,
    };
  }

  /**
   * 图
   * @param attributes
   * @param container
   */
  /*drawOtherShapes(attributes, container) {
    const width = 100;
    const height = 80;
    const imageX = -width / 2; // 图片显示在文字左边 4px 的位置
    const imageY = -height / 2 - 10;
    this.upsert(
      'iconImage',
      'image',
      {
        src: this.data.img || 'https://example.com/default-icon.png',
        href: this.data.img || 'https://example.com/default-icon.png',
        width: width,
        height: height,
        x: imageX,
        y: imageY,
        radius: [5, 5, 0, 0],
      },
      container,
    );
  }*/

  drawOtherShapes(attributes, container) {
    const width = 100;
    const height = 80;

    let bgc = '#182f4a';
    if (
      !(
        container.data.id == props.selectModel.code ||
        container.data.parentId == props.selectModel.code ||
        showGraphFlag.value
      )
    ) {
      //当
      bgc = 'rgb(179,172,172)';
    }

    const modelViewerHTML = `
    <model-viewer
      src="/platform-api/${container.data.modelUrl}"
      alt="3D 模型"
      class="model-viewer"
      style="
        width: ${width}px;
        height: ${height}px;
        background-color: ${bgc};
      "
      auto-rotate
      camera-controls
      disable-zoom
      interaction-prompt-style="none"
      disable-gesture-prompt
      touch-action="pan-y"
      exposure="1.0"
    ></model-viewer>
  `;

    this.upsert(
      'model-viewer',
      'html',
      {
        x: -width / 2,
        y: -height / 2 - 10,
        width: `${width}px`,
        height: `${height}px`,
        innerHTML: modelViewerHTML,
      },
      container,
    );
  }

  /**
   * 文字
   * @param attributes
   * @param container
   */
  /*  drawLabelShape(attributes, container) {
    // 示例：添加一个图片 + 文字标签
    const [width, height] = this.getSize(attributes);
    const span = this.upsert(
      'custom-span',
      'html',
      {
        x: -width / 2 + 8,
        y: 28,
        width: '100%',
        height: 24,
        innerHTML: '<div style="display: flex;flex-wrap: nowrap;font-size: 12px">' + container.data.name + '</div>',
      },
      container,
    );
  }*/
  drawLabelShape(attributes, container) {
    const [width, height] = this.getSize(attributes);
    let color = '#6c6666';
    if (container.data.parentId == props.selectModel.code || container.data.id == props.selectModel.code) {
      color = '#000';
    }
    this.upsert(
      'custom-span',
      'html',
      {
        x: -width / 2 + 8,
        y: 28,
        width: `${width - 16}px`, // 保证在节点宽度内（左右留 8px）
        height: '24px',
        innerHTML: `
        <div style="
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          font-size: 12px;
          white-space: nowrap;
          width: 100%;
          height: 100%;
          color:${color};
        ">
          ${container.data.name}
        </div>`,
      },
      container,
    );
  }

  drawBoxShape(attributes, container) {
    // 示例：添加一个图片 + 文字标签
    const [width, height] = this.getSize(attributes);
    const span = this.upsert(
      'custom-ellipse',
      'ellipse',
      {
        width: width,
        height: height,
      },
      container,
    );
  }

  drawEmptyShape(attributes, container) {
    // 示例：添加一个图片 + 文字标签
    const [width, height] = this.getSize(attributes);
    const span = this.upsert(
      'custom-span',
      'html',
      {
        x: -width / 2 + 25,
        y: -10,
        width: '100%',
        height: '100%',
        innerHTML: '<div style="display: flex;flex-wrap: nowrap;font-size: 12px;">进行关联</div>',
      },
      container,
    );
  }
  render(attributes = this.parsedAttributes, container) {
    super.render(attributes, container);
    /* console.log(container);*/
    if (container.data.type == 'empty') {
      //空
      this.drawEmptyShape(attributes, container);
      return;
    }

    if (container.data.id == props.selectModel.code && !showGraphFlag.value) {
      //当前这个元素数据 添加按钮
      this.drawAddNodeShape(attributes, container);
    }

    //加样式
    if (container.data.parentId == props.selectModel.code) {
      const { graph } = this.context;
      graph.updateNodeData([
        {
          id: container.data.id,
          style: { cursor: 'pointer' },
        },
      ]);
    }

    //图片
    this.drawOtherShapes(attributes, container);
    //文字
    this.drawLabelShape(attributes, container);
    //box
    this.drawBoxShape(attributes, container);
  }
}

register(ExtensionCategory.NODE, 'tree-node', TreeNode);

/**
 * 这里是点击拓扑图节点 事件
 * @param evt
 */
function handleNodeClick(evt) {
  const { target } = evt; // 获取被点击节点的 ID

  console.log('evt', evt);
  if (target.data.type == 'empty') {
    //显示dialog
    reset();
    newNode.value = target.data;
    open.value = true;
  } else {
    if (target.data.parentId !== props.selectModel.code) {
      return;
    } else {
      //编辑操作
      console.log('fromRelationData', fromRelationData);
      const row = fromRelationData.value.find((item) => item.to == target.data.id);
      form.value = row;
      const da = JSON.parse(row.data);
      form.value.style = da.styleType;
      form.value.x = da.position[0];
      form.value.y = da.position[1];
      form.value.z = da.position[2];
      form.value.name = da.text;
      console.log('row', row);
      //选中效果

      const findObj = props.modelList.find((item) => item.code == row.to);
      console.log('props.modelList.value', props.modelList);
      selectOpenModel.value = findObj;
      options.value = [selectOpenModel.value];
      // 等 DOM 更新后再设置选中项
      nextTick(() => {
        queryParams.value.selectModelCode = selectOpenModel.value.code;
      });
      open.value = true;
      //创建图像
      setTimeout(() => {
        const position = new THREE.Vector3(form.value.x, form.value.y, form.value.z);
        modelViewerCustom.value.addLabelAt(position, form.value.name);
      }, 2000);
    }
  }
}

function buildModelTree(dataList) {
  const nodes = new Map();
  const childToParent = new Map();

  // 第一步：把所有节点放入 Map
  dataList.forEach((item) => {
    const fromId = item.from;
    const toId = item.to;

    // from 节点
    if (!nodes.has(fromId)) {
      nodes.set(fromId, {
        id: fromId,
        name: item.fromName,
        modelUrl: item.fromModelUrl,
        parentId: '0',
        children: [],
        isCurrentModel: item.currentRelation ? item.currentRelation : false,
      });
    }

    // to 节点
    if (!nodes.has(toId)) {
      nodes.set(toId, {
        id: toId,
        name: item.toName,
        modelUrl: item.toModelUrl,
        parentId: fromId,
        children: [],
        isCurrentModel: item.currentRelation ? item.currentRelation : false,
      });
    } else {
      // 更新 parentId（如果之前加过但未赋 parentId）
      const node = nodes.get(toId);
      node.parentId = fromId;
    }

    // 建立父子关系
    childToParent.set(toId, fromId);
  });

  // 第二步：组装树结构
  const roots = [];

  // 找出所有没有父节点的根节点
  for (const [id, node] of nodes.entries()) {
    if (!childToParent.has(id)) {
      roots.push(node); // 没有父节点，即为根节点
    } else {
      const parentId = childToParent.get(id);
      const parent = nodes.get(parentId);
      parent.children.push(node);
    }
  }
  console.log('roots', roots);

  return roots;
}

function initGraph() {
  holo3dmodelrelationFromTopology({ modelCode: props.selectModel.code }).then((res) => {
    fromRelationData.value = JSON.parse(JSON.stringify(res.data));

    let tree = buildModelTree(res.data);

    // 👇 若为空数组或非数组，强制使用一个节点展示
    if (!Array.isArray(tree) || tree.length === 0) {
      tree = [
        {
          id: props.selectModel.code, // 注意：用 code 做 ID 才是你模型 ID
          name: props.selectModel.name || props.selectModel.code,
          modelUrl: props.selectModel.objectUrl,
          children: [], // 没有子节点
          isCurrentModel: false,
          parentId: 0,
        },
      ];
    }
    console.log('tree', tree);

    if (graph) {
      graph.destroy();
      graph = null;
    }
    graph = new Graph({
      container: containerRef.value,
      zoom: 0.1,
      zoomRange: [0.01, 1],
      data: treeToGraphData(tree[0], {
        getNodeData: (datum, depth) => {
          if (!datum) return {};
          datum.style = datum.style || {};
          datum.style.collapsed = false;
          /*   if (!datum.children) return datum;*/
          const children = Array.isArray(datum.children) ? datum.children : [];
          const { children: _, ...restDatum } = datum;
          return {
            ...restDatum,
            children: children.map((child) => {
              if (typeof child === 'string') return child; // 如果已经是 ID
              return child?.id || ''; // 否则取 ID
            }),
          };
        },
      }),
      node: {
        type: 'tree-node',
        style: {
          size: [100, 100],
          ports: [{ placement: 'left' }, { placement: 'right' }],
          radius: 4,
        },
      },
      edge: {
        type: 'cubic-horizontal',
        style: {
          stroke: GREY_COLOR,
        },
      },
      layout: {
        type: 'indented',
        direction: 'LR',
        dropCap: false,
        preventOverlap: true, // 防止节点重叠
        indent: 300,
        getHeight: () => 100,
        preLayout: false,
      },
      behaviors: ['drag-canvas'],
    });

    graph.once(GraphEvent.AFTER_RENDER, () => {
      graph.fitView();
    });
    graph.render();

    //添加
    graph.on('node:add', ({ nodeId }) => {
      //显示dialog
      //新增节点
      // 获取当前图的完整数据（新版G6用getData）
      const graphData = graph.getNodeData();
      const edgeData = graph.getEdgeData();

      function findNodeById(data, id) {
        for (const datum of data) {
          if (datum.id === id) return datum;
          if (datum.children) {
            for (const child of datum.children) {
              const found = findNodeById(child, id);
              if (found) return found;
            }
          }
        }
        return null;
      }

      const targetNode = findNodeById(graphData, nodeId);

      if (!targetNode) return;

      if (!targetNode.children) targetNode.children = [];

      const newNodeId = `new_${Date.now()}`;
      const newNode = {
        id: newNodeId,
        name: '点击进行关联',
        img: 'https://cdn.esg-cloud.com/VR/VRhtml0711/img/6895mini.56e46963.jpg',
        type: 'empty',
        isCurrentModel: false,
        /*
        label: '338.00',
        count: 123456,
        rate: 0.627,
        status: 'R',
        currency: 'Yuan',
        variableName: 'V2',
        variableValue: 0.179,
        variableUp: true,*/
        from: props.selectModel.code,
        parentId: props.selectModel.code,
        children: [],
      };
      // 添加到 targetNode 的 children 中
      targetNode.children.push(newNode);
      // 重新设置数据并渲染
      graph.addChildrenData(targetNode.id, [newNode]);
      graph.render();
    });
    graph.on(NodeEvent.CLICK, handleNodeClick);
  });
}

/*
function onDialogOpened(){

  showGraphStartPreview();
}*/

function showGraphStartPreview(code) {
  holo3dmodelrelationFromTopologyByChild({ modelCode: code }).then((res) => {
    /*   fromRelationData.value = JSON.parse(JSON.stringify(res.data));*/

    let tree = buildModelTree(res.data);

    // 👇 若为空数组或非数组，强制使用一个节点展示
    if (!Array.isArray(tree) || tree.length === 0) {
      tree = [
        {
          id: props.selectModel.code, // 注意：用 code 做 ID 才是你模型 ID
          name: props.selectModel.name || props.selectModel.code,
          modelUrl: props.selectModel.objectUrl,
          children: [], // 没有子节点
          isCurrentModel: false,
          parentId: 0,
        },
      ];
    }
    console.log('tree', tree);

    if (graphPreview) {
      graphPreview.destroy();
      graphPreview = null;
    }
    graphPreview = new Graph({
      container: showGraphRef.value,
      zoom: 0.1,
      zoomRange: [0.01, 1],
      data: treeToGraphData(tree[0], {
        getNodeData: (datum, depth) => {
          if (!datum) return {};
          datum.style = datum.style || {};
          datum.style.collapsed = false;
          /*   if (!datum.children) return datum;*/
          const children = Array.isArray(datum.children) ? datum.children : [];
          const { children: _, ...restDatum } = datum;
          return {
            ...restDatum,
            children: children.map((child) => {
              if (typeof child === 'string') return child; // 如果已经是 ID
              return child?.id || ''; // 否则取 ID
            }),
          };
        },
      }),
      node: {
        type: 'tree-node',
        style: {
          size: [100, 100],
          ports: [{ placement: 'left' }, { placement: 'right' }],
          radius: 4,
        },
      },
      edge: {
        type: 'cubic-horizontal',
        style: {
          stroke: GREY_COLOR,
        },
      },
      layout: {
        type: 'indented',
        direction: 'LR',
        dropCap: false,
        preventOverlap: true, // 防止节点重叠
        indent: 300,
        getHeight: () => 100,
        preLayout: false,
      },
      behaviors: ['drag-canvas'],
    });

    graphPreview.once(GraphEvent.AFTER_RENDER, () => {
      graphPreview.fitView();
    });
    graphPreview.render();
  });
}

onMounted(() => {
  initGraph();
  getList();
});
</script>
<style scoped>
.bg-woke {
  cursor: pointer;
  width: 200px;
  height: 44px;
  background-image: url(https://cdn.esg-cloud.com/VR/htmlImg/images/icon/long_board_in.png);
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-repeat: no-repeat;
  color: #fff;
}

.bg-woke2 {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
  color: rgb(255, 255, 255);
  width: 200px;
  height: 44px;
  border-radius: 10px;
}

.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}
.model-viewer {
  width: 100%;
  height: 200px;
  display: block;
}
.model-card {
  width: 100%;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.model-card.selected {
  border: 2px solid #4caf50 !important; /* 选中状态绿色边框 */
}

:deep(.el-card__body) {
  padding: 0;
}

.card-info {
  /*padding-top: 10px;*/
  /*text-align: center;*/
  display: flex;
  justify-content: space-between;
  padding: 5px;
}

.model-name {
  font-weight: bold;
}

.model-suffix {
  color: #999;
}

.relation_box {
  position: absolute;
  top: 20px;
  right: 100px;
}
</style>
