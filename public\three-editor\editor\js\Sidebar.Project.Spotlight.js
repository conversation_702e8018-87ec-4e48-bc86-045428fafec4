import { UIPanel, UIText, UIBreak, UIDiv } from './libs/ui.js';

function SidebarProjectSpotlight( editor ) {

	const container = new UIPanel();
	container.setId( 'spotlight' );

	// 标题
	container.add( new UIText( '亮点管理' ).setTextTransform( 'uppercase' ) );
	container.add( new UIBreak(), new UIBreak() );

	// 状态显示
	const statusText = new UIText( '正在加载亮点数据...' );
	container.add( statusText );
	container.add( new UIBreak() );

	// 工厂筛选容器
	const factoryFilterContainer = new UIDiv();
	factoryFilterContainer.dom.style.cssText = 'margin-bottom: 10px;';

	const factoryLabel = document.createElement( 'label' );
	factoryLabel.textContent = '工厂筛选: ';
	factoryLabel.style.cssText = 'color: #fff; font-size: 12px; margin-right: 8px;';

	const factorySelect = document.createElement( 'select' );
	factorySelect.style.cssText = `
		background: rgba(0,0,0,0.3);
		color: #fff;
		border: 1px solid #555;
		border-radius: 3px;
		padding: 4px 8px;
		font-size: 12px;
		width: 120px;
	`;
	factorySelect.innerHTML = '<option value="">全部工厂</option>';

	factoryFilterContainer.dom.appendChild( factoryLabel );
	factoryFilterContainer.dom.appendChild( factorySelect );
	container.add( factoryFilterContainer );
	container.add( new UIBreak() );

	// 亮点列表容器
	const spotlightListContainer = new UIDiv();
	spotlightListContainer.setClass( 'spotlight-list' );
	container.add( spotlightListContainer );

	/**
	 * 创建SVG图标元素
	 * 完全按照SvgIcon组件的方式创建
	 */
	function createSvgIcon( iconCode, size = '16px', color = 'white' ) {
		const svgIcon = document.createElement( 'svg' );
		svgIcon.className = 'svg-icon';
		svgIcon.setAttribute( 'aria-hidden', 'true' );

		// 设置样式，参考SvgIcon组件的CSS
		svgIcon.style.cssText = `
			width: ${size};
			height: ${size};
			position: relative;
			fill: ${color};
			vertical-align: -2px;
			display: inline-block;
		`;

		// 创建use元素，使用xlink:href
		const useElement = document.createElement( 'use' );
		useElement.setAttributeNS( 'http://www.w3.org/1999/xlink', 'xlink:href', `#icon-${iconCode}` );
		useElement.setAttribute( 'href', `#icon-${iconCode}` );

		svgIcon.appendChild( useElement );
		return svgIcon;
	}

	/**
	 * 检查SVG符号是否存在
	 */
	function checkSvgSymbolExists( iconCode ) {
		const symbolId = `#icon-${iconCode}`;
		const symbol = document.querySelector( symbolId );
		console.log( `Sidebar检查SVG符号 ${symbolId}:`, !!symbol );
		return !!symbol;
	}

	/**
	 * 创建备用图标
	 */
	function createFallbackIcon( iconCode, size = '16px', color = 'white' ) {
		const fallbackIcon = document.createElement( 'div' );
		fallbackIcon.style.cssText = `
			width: ${size};
			height: ${size};
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 12px;
			font-weight: bold;
			color: ${color};
			background: rgba(255,255,255,0.2);
			border-radius: 2px;
		`;
		fallbackIcon.textContent = iconCode ? iconCode.charAt(0).toUpperCase() : '?';
		return fallbackIcon;
	}

	// 存储数据
	let allSpotlightData = [];
	let factoryData = [];

	// 设置亮点数据的方法
	function setSpotlightData( data ) {
		console.log( '🌟 Sidebar收到亮点数据:', data );
		allSpotlightData = data || [];
		renderSpotlightList( allSpotlightData );
	}

	// 设置工厂数据的方法
	function setFactoryData( data ) {
		console.log( '🏭 Sidebar收到工厂数据:', data );
		factoryData = data || [];

		// 更新工厂选择器
		factorySelect.innerHTML = '<option value="">全部工厂</option>';
		factoryData.forEach( factory => {
			const option = document.createElement( 'option' );
			option.value = factory.code;
			option.textContent = factory.name;
			factorySelect.appendChild( option );
		});
	}

	// 渲染亮点列表
	function renderSpotlightList( data ) {
		if ( !data || data.length === 0 ) {
			statusText.setValue( '暂无亮点数据' );
			spotlightListContainer.dom.innerHTML = '';
			return;
		}

		statusText.setValue( `已加载 ${data.length} 个亮点` );

		// 清空现有内容
		spotlightListContainer.dom.innerHTML = '';

		// 创建亮点列表
		data.forEach( ( spotlight, index ) => {
			const itemDiv = document.createElement( 'div' );
			itemDiv.className = 'spotlight-item';
			itemDiv.style.cssText = `
				display: flex;
				align-items: center;
				padding: 8px;
				margin-bottom: 4px;
				background: rgba(0,0,0,0.1);
				border-radius: 4px;
				cursor: pointer;
				transition: background-color 0.2s;
			`;

			// 图标容器
			const iconContainer = document.createElement( 'div' );
			iconContainer.className = 'spotlight-icon';
			iconContainer.style.cssText = `
				width: 24px;
				height: 24px;
				margin-right: 8px;
				display: flex;
				justify-content: center;
				align-items: center;
				border-radius: 6px;
				background-color: ${spotlight.iconColor || '#b2c2c2'};
				flex-shrink: 0;
			`;

			// 创建图标
			if ( spotlight.iconCode ) {
				console.log( `🎨 Sidebar尝试显示亮点图标: ${spotlight.iconCode}` );

				if ( checkSvgSymbolExists( spotlight.iconCode ) ) {
					const svgIcon = createSvgIcon( spotlight.iconCode, '16px', 'white' );
					iconContainer.appendChild( svgIcon );
					console.log( `✅ Sidebar使用SVG符号显示图标: ${spotlight.iconCode}` );
				} else {
					console.warn( `⚠️ Sidebar SVG符号不存在: ${spotlight.iconCode}，使用备用图标` );
					const fallbackIcon = createFallbackIcon( spotlight.iconCode, '16px', 'white' );
					iconContainer.appendChild( fallbackIcon );
				}
			} else {
				console.log( '📝 Sidebar亮点没有iconCode，使用默认图标' );
				const defaultIcon = createFallbackIcon( '', '16px', 'white' );
				iconContainer.appendChild( defaultIcon );
			}

			// 文本内容
			const textContainer = document.createElement( 'div' );
			textContainer.style.cssText = 'flex: 1; overflow: hidden;';

			const nameSpan = document.createElement( 'div' );
			nameSpan.style.cssText = 'font-size: 12px; color: #fff; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;';
			const fullName = spotlight.nameI18n?.zhContent || spotlight.name || `亮点${index + 1}`;
			nameSpan.textContent = fullName;
			// 添加鼠标悬浮显示完整名称
			nameSpan.title = fullName;

			const typeSpan = document.createElement( 'div' );
			typeSpan.style.cssText = 'font-size: 10px; color: #ccc; margin-top: 2px;';
			typeSpan.textContent = spotlight.typeCode || '';

			textContainer.appendChild( nameSpan );
			textContainer.appendChild( typeSpan );

			// 组装元素
			itemDiv.appendChild( iconContainer );
			itemDiv.appendChild( textContainer );

			// 添加悬停效果
			itemDiv.addEventListener( 'mouseenter', () => {
				itemDiv.style.backgroundColor = 'rgba(64, 158, 255, 0.2)';
			} );
			itemDiv.addEventListener( 'mouseleave', () => {
				itemDiv.style.backgroundColor = 'rgba(0,0,0,0.1)';
			} );

			// 添加点击事件（可以扩展为添加到场景等功能）
			itemDiv.addEventListener( 'click', () => {
				console.log( '点击亮点:', spotlight );
				// 这里可以添加将亮点添加到场景的逻辑
			} );

			spotlightListContainer.dom.appendChild( itemDiv );
		} );
	}

	// 筛选功能
	function filterSpotlights() {
		const selectedFactory = factorySelect.value;

		let filteredData = allSpotlightData;
		if ( selectedFactory ) {
			filteredData = allSpotlightData.filter( spotlight =>
				spotlight.factoryCode === selectedFactory
			);
		}

		renderSpotlightList( filteredData );

		// 更新状态显示
		if ( selectedFactory ) {
			statusText.setValue( `显示 ${filteredData.length} / ${allSpotlightData.length} 个亮点` );
		} else {
			statusText.setValue( `已加载 ${allSpotlightData.length} 个亮点` );
		}
	}

	// 绑定工厂筛选事件
	factorySelect.addEventListener( 'change', filterSpotlights );

	// 暴露方法给外部调用
	container.setSpotlightData = setSpotlightData;
	container.setFactoryData = setFactoryData;

	return container;

}

export { SidebarProjectSpotlight };
