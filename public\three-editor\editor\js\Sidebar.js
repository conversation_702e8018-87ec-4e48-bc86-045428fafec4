import { UITabbedPanel, UISpan } from './libs/ui.js';

import { SidebarScene } from './Sidebar.Scene.js';
import { SidebarProperties } from './Sidebar.Properties.js';
import { SidebarProject } from './Sidebar.Project.js';
import { SidebarSettings } from './Sidebar.Settings.js';
import { SidebarSpotlight } from './Sidebar.Spotlight.js';

function Sidebar( editor ) {

	const strings = editor.strings;

	const container = new UITabbedPanel();
	container.setId( 'sidebar' );

	const sidebarProperties = new SidebarProperties( editor );

	const scene = new UISpan().add(
		new SidebarScene( editor ),
		sidebarProperties
	);
	const project = new SidebarProject( editor );
	console.log( '🌟 正在创建亮点侧边栏...' );
	const spotlight = new SidebarSpotlight( editor );
	console.log( '🌟 亮点侧边栏创建完成' );
	const settings = new SidebarSettings( editor );

	container.addTab( 'scene', strings.getKey( 'sidebar/scene' ), scene );
	container.addTab( 'project', strings.getKey( 'sidebar/project' ), project );
	console.log( '🌟 正在添加亮点选项卡...' );
	container.addTab( 'spotlight', '亮点', spotlight );
	console.log( '🌟 亮点选项卡已添加' );
	container.addTab( 'settings', strings.getKey( 'sidebar/settings' ), settings );
	// 默认选中场景选项卡
	container.select( 'scene' );

	const sidebarPropertiesResizeObserver = new ResizeObserver( function () {

		sidebarProperties.tabsDiv.setWidth( getComputedStyle( container.dom ).width );

	} );

	sidebarPropertiesResizeObserver.observe( container.tabsDiv.dom );

	// 暴露设置亮点数据的方法
	container.setSpotlightData = function( data ) {
		if ( spotlight && spotlight.setSpotlightData ) {
			spotlight.setSpotlightData( data );
		}
	};

	// 暴露设置工厂数据的方法
	container.setFactoryData = function( data ) {
		if ( spotlight && spotlight.setFactoryData ) {
			spotlight.setFactoryData( data );
		}
	};

	return container;

}

export { Sidebar };
