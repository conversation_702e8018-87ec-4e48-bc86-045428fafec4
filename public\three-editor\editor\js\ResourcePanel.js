/**
 * ResourcePanel - 资源面板组件
 *
 * 这是Three.js编辑器的左侧资源面板，提供了分类按钮式的资源管理功能。
 * 包含几何体、灯光、模型等分类资源，支持拖拽到场景中创建对象。
 *
 * 主要功能：
 * - 分类按钮式展开/收缩设计
 * - 支持拖拽添加到场景
 * - 面板展开/收缩功能
 * - 键盘快捷键支持（Ctrl+R）
 * - 响应式布局适配
 *
 * <AUTHOR> Editor Team
 */

import { UIPanel, UIDiv, UIText } from './libs/ui.js';
import * as THREE from 'three';
import { AddObjectCommand } from './commands/AddObjectCommand.js';

/**
 * 创建SVG图标元素
 * 完全按照spotlightType页面的HTML结构创建
 */
function createSvgIcon( iconCode, size = '16px', color = 'white' ) {
	// 方法1：直接设置innerHTML，完全复制spotlightType的结构
	const svgContainer = document.createElement( 'div' );
	svgContainer.innerHTML = `
		<svg class="svg-icon el-input__icon" aria-hidden="true" style="width: ${size}; height: ${size}; fill: ${color};">
			<use xlink:href="#icon-${iconCode}"></use>
		</svg>
	`;
	return svgContainer.firstElementChild;
}

/**
 * 创建SVG图标元素 - 备用方法
 * 使用DOM API创建，但完全模拟spotlightType的结构
 */
function createSvgIconDOM( iconCode, size = '16px', color = 'white' ) {
	const svgIcon = document.createElement( 'svg' );
	svgIcon.className = 'svg-icon el-input__icon';
	svgIcon.setAttribute( 'aria-hidden', 'true' );

	// 设置样式
	svgIcon.style.width = size;
	svgIcon.style.height = size;
	svgIcon.style.fill = color;

	// 创建use元素
	const useElement = document.createElement( 'use' );
	useElement.setAttributeNS( 'http://www.w3.org/1999/xlink', 'xlink:href', `#icon-${iconCode}` );

	svgIcon.appendChild( useElement );
	return svgIcon;
}

/**
 * 检查SVG符号是否存在
 */
function checkSvgSymbolExists( iconCode ) {
	const symbolId = `#icon-${iconCode}`;
	const symbol = document.querySelector( symbolId );
	console.log( `检查SVG符号 ${symbolId}:`, !!symbol );
	return !!symbol;
}

/**
 * 调试：列出所有可用的SVG符号
 */
function listAvailableSvgSymbols() {
	const symbols = document.querySelectorAll( 'symbol[id^="icon-"]' );
	const symbolIds = Array.from( symbols ).map( s => s.id );
	console.log( '🔍 可用的SVG符号:', symbolIds );
	return symbolIds;
}

/**
 * 创建备用图标（当SVG符号不存在时使用）
 */
function createFallbackIcon( iconCode, size = '16px', color = 'white' ) {
	const fallbackIcon = document.createElement( 'div' );
	fallbackIcon.style.cssText = `
		width: ${size};
		height: ${size};
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 12px;
		font-weight: bold;
		color: ${color};
		background: rgba(255,255,255,0.2);
		border-radius: 2px;
	`;
	fallbackIcon.textContent = iconCode ? iconCode.charAt(0).toUpperCase() : '?';
	return fallbackIcon;
}

/**
 * ResourcePanel构造函数
 * 创建并初始化资源面板组件
 *
 * @param {Editor} editor - 编辑器实例，用于访问信号系统和其他组件
 */
function ResourcePanel( editor ) {

	// 获取编辑器的信号系统和字符串管理器
	const signals = editor.signals;
	const strings = editor.strings;

	// 存储外部传入的模型库数据
	let externalModelLibraryData = null;

	// 存储外部传入的亮点数据
	let externalSpotlightData = [];

	// 存储外部传入的工厂数据
	let externalFactoryData = [];

	/**
	 * 主容器 - 资源面板的根容器
	 */
	const container = new UIPanel();
	container.setId( 'resourcePanel' );

	/**
	 * 分类按钮容器 - 左侧分类按钮区域
	 */
	const categoryButtons = new UIDiv();
	categoryButtons.setClass( 'category-buttons' );
	container.add( categoryButtons );

	/**
	 * 详细内容区域 - 右侧展开的详细选项
	 */
	const detailContent = new UIDiv();
	detailContent.setClass( 'detail-content' );
	container.add( detailContent );

	/**
	 * 切换按钮 - 用于展开/收缩面板
	 */
	const toggleBtn = new UIDiv();
	toggleBtn.setClass( 'toggle-btn' );
	toggleBtn.dom.innerHTML = '◀'; // 左箭头表示可以收缩
	toggleBtn.dom.title = 'Toggle Resource Panel (Ctrl+R)';
	container.add( toggleBtn );

	/**
	 * 面板收缩状态标志
	 * @type {boolean}
	 */
	let isCollapsed = false;

	/**
	 * 当前激活的分类
	 * @type {string}
	 */
	let activeCategory = null;

	/**
	 * 切换按钮点击事件处理
	 * 控制面板的展开/收缩，并调整其他组件的布局
	 */
	toggleBtn.onClick( function () {

		isCollapsed = !isCollapsed;

		if ( isCollapsed ) {
			// 收缩面板
			container.addClass( 'collapsed' );
			toggleBtn.dom.innerHTML = '▶';  // 右箭头表示可以展开

			// 调整其他组件位置，面板完全收缩，从左边缘开始
			document.getElementById( 'viewport' ).style.left = '0px';
			document.getElementById( 'script' ).style.left = '0px';
			document.getElementById( 'player' ).style.left = '0px';
			// 工具栏移动到左侧
			const toolbar = document.getElementById( 'toolbar' );
			if ( toolbar ) {
				toolbar.style.left = '10px';
			}
			// 调整resizer位置 - 收缩时不需要调整，保持原位置
			const resizer = document.getElementById( 'resizer' );
			if ( resizer ) {
				// 收缩时保持resizer在原位置，因为sidebar位置没变
				// resizer的right值应该与sidebar的宽度一致
				const sidebar = document.getElementById( 'sidebar' );
				const sidebarWidth = sidebar ? parseInt( sidebar.style.width ) || 350 : 350;
				resizer.style.right = sidebarWidth + 'px';
			}
		} else {
			// 展开面板
			container.removeClass( 'collapsed' );
			toggleBtn.dom.innerHTML = '◀';  // 左箭头表示可以收缩

			// 恢复其他组件位置，添加280px左边距
			document.getElementById( 'viewport' ).style.left = '280px';
			document.getElementById( 'script' ).style.left = '280px';
			document.getElementById( 'player' ).style.left = '280px';
			// 工具栏恢复到原位置
			const toolbar = document.getElementById( 'toolbar' );
			if ( toolbar ) {
				toolbar.style.left = '290px';
			}
			// 恢复resizer位置
			const resizer = document.getElementById( 'resizer' );
			if ( resizer ) {
				// 展开时恢复resizer到正确位置
				const sidebar = document.getElementById( 'sidebar' );
				const sidebarWidth = sidebar ? parseInt( sidebar.style.width ) || 350 : 350;
				resizer.style.right = sidebarWidth + 'px';
			}
		}

		// 触发窗口大小调整信号，让其他组件重新计算布局
		signals.windowResize.dispatch();

	} );

	/**
	 * 创建分类按钮
	 * 根据提供的分类信息创建左侧的分类按钮
	 *
	 * @param {string} id - 分类ID
	 * @param {string} title - 分类标题
	 * @param {string} icon - 分类图标
	 * @param {Array} items - 分类下的资源项
	 * @returns {UIDiv} 创建的分类按钮
	 */
	function createCategoryButton( id, title, icon, items ) {

		const button = new UIDiv();
		button.setClass( 'category-button' );
		button.dom.dataset.category = id;

		// 按钮图标
		const buttonIcon = new UIDiv();
		buttonIcon.setClass( 'category-icon' );
		buttonIcon.dom.innerHTML = icon;
		button.add( buttonIcon );

		// 按钮标题
		const buttonTitle = new UIDiv();
		buttonTitle.setClass( 'category-title' );
		buttonTitle.dom.textContent = title;
		button.add( buttonTitle );

		// 点击事件处理
		button.onClick( function () {

			// 如果点击的是当前激活的分类，则收缩
			if ( activeCategory === id ) {
				activeCategory = null;
				detailContent.dom.innerHTML = '';
				detailContent.removeClass( 'active' );
				// 移除所有按钮的激活状态
				const allButtons = categoryButtons.dom.querySelectorAll( '.category-button' );
				allButtons.forEach( btn => btn.classList.remove( 'active' ) );
			} else {
				// 激活新的分类
				activeCategory = id;
				// 检查是否为特殊分类
				const category = categories.find( cat => cat.id === id );
				const isSpecial = category && category.isSpecial;
				showCategoryDetail( title, items, isSpecial );
				detailContent.addClass( 'active' );
				// 更新按钮激活状态
				const allButtons = categoryButtons.dom.querySelectorAll( '.category-button' );
				allButtons.forEach( btn => btn.classList.remove( 'active' ) );
				button.addClass( 'active' );
			}

		} );

		return button;

	}

	/**
	 * 显示分类详细内容
	 * 在右侧详细内容区域显示选中分类的资源项
	 *
	 * @param {string} title - 分类标题
	 * @param {Array} items - 资源项数组
	 * @param {boolean} isSpecial - 是否为特殊分类（如模型库）
	 */
	function showCategoryDetail( title, items, isSpecial = false ) {

		// 清空详细内容区域
		detailContent.dom.innerHTML = '';

		// 如果是模型库，显示特殊界面
		if ( isSpecial && title === 'Model Library' ) {
			showModelLibrary();
			return;
		}

		// 如果是亮点，显示亮点界面
		if ( isSpecial && title === 'Spotlight' ) {
			showSpotlightLibrary();
			return;
		}

		// 创建详细内容标题
		const detailTitle = document.createElement( 'div' );
		detailTitle.className = 'detail-title';
		detailTitle.textContent = title;
		detailContent.dom.appendChild( detailTitle );

		// 创建资源网格
		const grid = document.createElement( 'div' );
		grid.className = 'detail-grid';
		detailContent.dom.appendChild( grid );

		// 添加资源项
		items.forEach( item => {

			const resourceItem = createResourceItem( item );
			grid.appendChild( resourceItem );

		} );

	}

	/**
	 * 显示模型库界面
	 * 创建类似图片中的模型库布局
	 */
	function showModelLibrary() {

		// 创建模型库容器
		const libraryContainer = document.createElement( 'div' );
		libraryContainer.className = 'model-library-container';
		detailContent.dom.appendChild( libraryContainer );

		// 创建标签页
		const tabsContainer = document.createElement( 'div' );
		tabsContainer.className = 'library-tabs';
		libraryContainer.appendChild( tabsContainer );

		const modelLibraryTab = document.createElement( 'div' );
		modelLibraryTab.className = 'library-tab active';
		modelLibraryTab.textContent = '模型库';
		tabsContainer.appendChild( modelLibraryTab );

		const myModelsTab = document.createElement( 'div' );
		myModelsTab.className = 'library-tab';
		myModelsTab.textContent = '我的';
		tabsContainer.appendChild( myModelsTab );

		// 创建搜索框
		const searchContainer = document.createElement( 'div' );
		searchContainer.className = 'library-search';
		libraryContainer.appendChild( searchContainer );

		const searchInput = document.createElement( 'input' );
		searchInput.type = 'text';
		searchInput.placeholder = '搜索模型...';
		searchInput.className = 'library-search-input';
		searchContainer.appendChild( searchInput );

		const searchButton = document.createElement( 'button' );
		searchButton.className = 'library-search-btn';
		searchButton.innerHTML = '🔍';
		searchContainer.appendChild( searchButton );

		// 创建内容区域容器
		const contentContainer = document.createElement( 'div' );
		contentContainer.className = 'library-content-container';
		libraryContainer.appendChild( contentContainer );

		// 创建系统模型库内容
		const systemContent = document.createElement( 'div' );
		systemContent.className = 'library-content system-content active';
		contentContainer.appendChild( systemContent );

		// 创建用户模型库内容
		const userContent = document.createElement( 'div' );
		userContent.className = 'library-content user-content';
		contentContainer.appendChild( userContent );

		// 渲染系统模型库
		renderSystemLibrary( systemContent );

		// 渲染用户模型库
		renderUserLibrary( userContent );

		// 标签页切换功能
		modelLibraryTab.addEventListener( 'click', function () {
			// 切换标签页状态
			modelLibraryTab.classList.add( 'active' );
			myModelsTab.classList.remove( 'active' );

			// 切换内容显示
			systemContent.classList.add( 'active' );
			userContent.classList.remove( 'active' );
		} );

		myModelsTab.addEventListener( 'click', function () {
			// 切换标签页状态
			myModelsTab.classList.add( 'active' );
			modelLibraryTab.classList.remove( 'active' );

			// 切换内容显示
			userContent.classList.add( 'active' );
			systemContent.classList.remove( 'active' );
		} );

	}

	/**
	 * 递归渲染多层文件夹结构 - 支持文件和文件夹混合，保留所有层级
	 */
	function renderNestedCategories( container, categories, level = 0 ) {
		categories.forEach( ( category, index ) => {
			if ( category.isFolder ) {
				// 这是一个文件夹，创建文件夹容器（无论是否有内容）
				const folderElement = createLibraryFolder( category.name, category.children, level, index === 0 );
				container.appendChild( folderElement );
			} else {
				// 这是一个模型分类，需要区分文件和子文件夹
				const models = [];
				const subFolders = [];

				if ( category.children ) {
					category.children.forEach( child => {
						if ( child.type === 'file' ) {
							// 这是一个文件
							models.push({
								name: child.name || child.filename,
								image: getThumbnailByType( child.suffix ) || '📦',
								id: child.code,
								url: child.localUrl,
								filename: child.filename,
								suffix: child.suffix
							});
						} else {
							// 这是一个子文件夹
							subFolders.push( child );
						}
					});
				}

				// 如果有子文件夹，创建文件夹容器来保持层级结构
				if ( subFolders.length > 0 ) {
					const folderElement = createLibraryFolder( category.name, [], level, index === 0 );
					container.appendChild( folderElement );

					// 获取文件夹内容容器
					const folderContent = folderElement.querySelector( '.library-folder-content' );
					if ( folderContent ) {
						// 如果当前分类有模型文件，直接添加模型（不创建额外的"模型文件"分类）
						if ( models.length > 0 ) {
							const modelsGrid = document.createElement( 'div' );
							modelsGrid.className = 'library-models-grid';
							folderContent.appendChild( modelsGrid );

							models.forEach( model => {
								const modelItem = createModelItem( model );
								modelsGrid.appendChild( modelItem );
							});
						}

						// 递归处理子文件夹
						renderNestedCategories( folderContent, subFolders, level + 1 );
					}
				} else {
					// 叶子节点，创建模型分类（即使为空也要显示）
					const categoryElement = createLibraryCategory( category.name, models, index === 0 );
					container.appendChild( categoryElement );
				}
			}
		});
	}

	/**
	 * 渲染系统模型库内容 - 支持多层结构
	 */
	function renderSystemLibrary( container ) {
		// 使用外部数据或默认数据
		const systemLibrary = externalModelLibraryData?.systemLibrary || [
			{
				code: 'furniture',
				name: '家具',
				children: [
					{ code: 'shelf-a', name: '货架A', thumbnail: '📦', localUrl: 'models/furniture/shelf-a.glb' },
					{ code: 'shelf-b', name: '货架B', thumbnail: '📦', localUrl: 'models/furniture/shelf-b.glb' }
				],
				isFolder: false
			}
		];

		// 创建分类容器
		const categoriesContainer = document.createElement( 'div' );
		categoriesContainer.className = 'library-categories';
		container.appendChild( categoriesContainer );

		// 递归渲染多层分类结构
		renderNestedCategories( categoriesContainer, systemLibrary, 0 );
	}

	/**
	 * 渲染用户模型库内容 - 只渲染"全部"分类，包含完整的子文件夹层级
	 */
	function renderUserLibrary( container ) {
		// 使用外部数据或默认数据
		const userLibrary = externalModelLibraryData?.userLibrary || [
			{
				code: 'my-models',
				name: '我的模型',
				children: [
					{ code: 'custom-1', name: '自定义模型1', thumbnail: '📁', localUrl: 'models/user/custom-1.glb' }
				]
			}
		];

		console.log('渲染用户模型库，数据结构:', userLibrary);

		// 创建分类容器
		const categoriesContainer = document.createElement( 'div' );
		categoriesContainer.className = 'library-categories';
		container.appendChild( categoriesContainer );

		// 如果没有用户模型库数据，显示提示
		if ( userLibrary.length === 0 ) {
			const emptyMessage = document.createElement( 'div' );
			emptyMessage.className = 'library-empty-message';
			emptyMessage.innerHTML = `
				<div class="empty-icon">📁</div>
				<div class="empty-text">暂无个人模型</div>
				<div class="empty-hint">您可以上传自己的3D模型文件</div>
			`;
			categoriesContainer.appendChild( emptyMessage );
			return;
		}

		// 只渲染第一个分类（通常是"全部"），但包含完整的子文件夹层级
		if ( userLibrary.length > 0 ) {
			const mainCategory = userLibrary[0]; // 只取第一个分类（"全部"）
			renderUserLibraryCategories( categoriesContainer, [mainCategory], 0 );
		}
	}

	/**
	 * 递归渲染用户模型库分类 - 所有文件夹都挂在"全部"下面
	 */
	function renderUserLibraryCategories( container, categories, level ) {
		categories.forEach( ( category, index ) => {
			// 提取文件类型的子项（模型文件）
			const models = category.children
				.filter( child => child.type === 'file' )
				.map( child => ({
					name: child.name || child.filename,
					image: getThumbnailByType( child.suffix ) || '📁',
					id: child.code,
					url: child.localUrl,
					filename: child.filename,
					suffix: child.suffix
				}));

			// 提取文件夹类型的子项
			const subFolders = category.children.filter( child => child.type !== 'file' );

			console.log(`分类 "${category.name}" - 模型数量: ${models.length}, 子文件夹数量: ${subFolders.length}`);

			// 特殊处理"全部"分类：创建一个包含所有内容的分类
			if ( category.name === '全部' ) {
				// 创建"全部"分类容器
				const categoryElement = createLibraryCategory( category.name, [], true );
				container.appendChild( categoryElement );

				// 获取分类内容容器
				const categoryContent = categoryElement.querySelector( '.library-category-content' );
				if ( categoryContent ) {
					// 如果有根级别的模型文件，直接添加到网格中
					if ( models.length > 0 ) {
						const modelsGrid = document.createElement( 'div' );
						modelsGrid.className = 'library-models-grid';
						categoryContent.appendChild( modelsGrid );

						models.forEach( model => {
							const modelItem = createModelItem( model );
							modelsGrid.appendChild( modelItem );
						});
					}

					// 如果有子文件夹，递归处理并添加到"全部"分类下
					if ( subFolders.length > 0 ) {
						renderUserLibraryCategories( categoryContent, subFolders, level + 1 );
					}
				}
			} else {
				// 其他分类的处理逻辑（作为"全部"的子项）
				if ( subFolders.length > 0 ) {
					// 有子文件夹，创建文件夹容器
					const folderElement = createLibraryFolder( category.name, [], level, false );
					container.appendChild( folderElement );

					// 获取文件夹内容容器
					const folderContent = folderElement.querySelector( '.library-folder-content' );
					if ( folderContent ) {
						// 如果当前文件夹有模型文件，直接添加到网格中
						if ( models.length > 0 ) {
							const modelsGrid = document.createElement( 'div' );
							modelsGrid.className = 'library-models-grid';
							folderContent.appendChild( modelsGrid );

							models.forEach( model => {
								const modelItem = createModelItem( model );
								modelsGrid.appendChild( modelItem );
							});
						}

						// 递归处理子文件夹
						renderUserLibraryCategories( folderContent, subFolders, level + 1 );
					}
				} else {
					// 叶子节点，创建模型分类（即使为空也要显示）
					const categoryElement = createLibraryCategory( category.name, models, false );
					container.appendChild( categoryElement );
				}
			}
		});
	}

	/**
	 * 根据文件后缀获取缩略图
	 */
	function getThumbnailByType( suffix ) {
		const thumbnailMap = {
			'glb': '📦',
			'gltf': '📦',
			'fbx': '🎭',
			'obj': '🔷',
			'dae': '🎨',
			'3ds': '🏗️',
			'ply': '💎',
			'stl': '⚙️'
		};
		return thumbnailMap[suffix?.toLowerCase()] || '📁';
	}

	/**
	 * 创建单个模型项
	 */
	function createModelItem( model ) {
		const modelItem = document.createElement( 'div' );
		modelItem.className = 'library-model-item';
		modelItem.draggable = true;

		const modelImage = document.createElement( 'div' );
		modelImage.className = 'library-model-image';
		modelImage.textContent = model.image;
		modelItem.appendChild( modelImage );

		const modelName = document.createElement( 'div' );
		modelName.className = 'library-model-name';
		modelName.textContent = model.name;
		modelItem.appendChild( modelName );

		// 拖拽功能
		modelItem.addEventListener( 'dragstart', function ( event ) {
			// 确保URL经过正确的处理
			const modelUrl = model.url || `models/${model.id}.glb`;
			console.log('🔍 Model Library - 原始URL:', modelUrl);
			console.log('🔍 Model Library - 模型数据:', model);

			event.dataTransfer.setData( 'application/json', JSON.stringify( {
				type: 'model',
				data: {
					id: model.id,
					name: model.name,
					url: modelUrl  // 这里的URL会在createModelObject中通过buildPlatformApiUrl处理
				}
			} ) );
			event.dataTransfer.effectAllowed = 'copy';
		} );

		return modelItem;
	}

	/**
	 * 显示亮点库界面
	 * 创建类似模型库的亮点管理布局
	 */
	function showSpotlightLibrary() {

		// 创建亮点库容器
		const spotlightContainer = document.createElement( 'div' );
		spotlightContainer.className = 'spotlight-library-container';
		detailContent.dom.appendChild( spotlightContainer );

		// 创建标题
		const titleContainer = document.createElement( 'div' );
		titleContainer.className = 'spotlight-title';
		titleContainer.textContent = '亮点管理';
		spotlightContainer.appendChild( titleContainer );

		// 创建搜索和筛选区域
		const filterContainer = document.createElement( 'div' );
		filterContainer.className = 'spotlight-filters';
		spotlightContainer.appendChild( filterContainer );

		// 搜索框
		const searchContainer = document.createElement( 'div' );
		searchContainer.className = 'spotlight-search';
		filterContainer.appendChild( searchContainer );

		const searchInput = document.createElement( 'input' );
		searchInput.type = 'text';
		searchInput.placeholder = '搜索亮点...';
		searchInput.className = 'spotlight-search-input';
		searchContainer.appendChild( searchInput );

		// 工厂筛选
		const factorySelect = document.createElement( 'select' );
		factorySelect.className = 'spotlight-factory-select';
		factorySelect.innerHTML = '<option value="">全部工厂</option>';

		// 添加工厂选项
		if ( externalFactoryData && externalFactoryData.length > 0 ) {
			externalFactoryData.forEach( factory => {
				const option = document.createElement( 'option' );
				option.value = factory.code;
				option.textContent = factory.name;
				factorySelect.appendChild( option );
			});
		}
		filterContainer.appendChild( factorySelect );

		// 类型筛选
		const typeSelect = document.createElement( 'select' );
		typeSelect.className = 'spotlight-type-select';
		typeSelect.innerHTML = `
			<option value="">全部类型</option>
			<option value="1">文字</option>
			<option value="2">语音</option>
			<option value="3">图片</option>
			<option value="4">视频</option>
			<option value="5">看板</option>
			<option value="6">富文本</option>
			<option value="7">标识牌</option>
			<option value="8">监控</option>
			<option value="9">联网设备</option>
		`;
		filterContainer.appendChild( typeSelect );

		// 状态显示
		const statusContainer = document.createElement( 'div' );
		statusContainer.className = 'spotlight-status';
		statusContainer.textContent = `已加载 ${externalSpotlightData.length} 个亮点`;
		spotlightContainer.appendChild( statusContainer );

		// 亮点列表容器
		const listContainer = document.createElement( 'div' );
		listContainer.className = 'spotlight-list';
		spotlightContainer.appendChild( listContainer );

		// 渲染亮点列表
		function renderSpotlightList( data = externalSpotlightData ) {
			listContainer.innerHTML = '';

			if ( data.length === 0 ) {
				const emptyMessage = document.createElement( 'div' );
				emptyMessage.className = 'spotlight-empty-message';
				emptyMessage.innerHTML = `
					<div class="empty-icon">🌟</div>
					<div class="empty-text">暂无亮点数据</div>
					<div class="empty-hint">请检查数据源或联系管理员</div>
				`;
				listContainer.appendChild( emptyMessage );
				return;
			}

			data.forEach( spotlight => {
				const spotlightItem = createSpotlightItem( spotlight );
				listContainer.appendChild( spotlightItem );
			});
		}

		// 创建亮点项
		function createSpotlightItem( spotlight ) {
			const itemContainer = document.createElement( 'div' );
			itemContainer.className = 'spotlight-item';
			itemContainer.style.display = 'flex';
			itemContainer.style.alignItems = 'center';
			itemContainer.style.padding = '8px';
			itemContainer.style.border = '1px solid #eee';
			itemContainer.style.marginBottom = '5px';
			itemContainer.style.borderRadius = '3px';

			// 创建icon容器
			const iconContainer = document.createElement( 'div' );
			iconContainer.className = 'spotlight-icon';
			iconContainer.style.width = '24px';
			iconContainer.style.height = '24px';
			iconContainer.style.marginRight = '8px';
			iconContainer.style.display = 'flex';
			iconContainer.style.justifyContent = 'center';
			iconContainer.style.alignItems = 'center';
			iconContainer.style.borderRadius = '6px';
			iconContainer.style.backgroundColor = spotlight.iconColor || '#b2c2c2';
			iconContainer.style.flexShrink = '0';

			// 创建SVG图标
			if ( spotlight.iconCode ) {
				console.log( `🎨 尝试显示亮点图标: ${spotlight.iconCode}` );

				// 首次调用时列出所有可用符号（仅调试用）
				if ( !window._svgSymbolsListed ) {
					listAvailableSvgSymbols();
					window._svgSymbolsListed = true;
				}

				// 首先检查SVG符号是否存在
				if ( checkSvgSymbolExists( spotlight.iconCode ) ) {
					// 尝试方法1：使用innerHTML方式创建
					const svgIcon1 = createSvgIcon( spotlight.iconCode, '16px', 'white' );
					iconContainer.appendChild( svgIcon1 );
					console.log( `✅ 使用SVG符号显示图标 (方法1): ${spotlight.iconCode}` );

					// 调试：检查创建的SVG元素
					console.log( '🔍 创建的SVG元素 (方法1):', svgIcon1 );
					console.log( '🔍 SVG元素的HTML (方法1):', svgIcon1.outerHTML );

					// 尝试方法2：使用DOM API方式创建（作为对比）
					const svgIcon2 = createSvgIconDOM( spotlight.iconCode, '16px', 'white' );
					console.log( '🔍 创建的SVG元素 (方法2):', svgIcon2 );
					console.log( '🔍 SVG元素的HTML (方法2):', svgIcon2.outerHTML );

					// 调试：检查父容器
					console.log( '🔍 图标容器:', iconContainer );
					console.log( '🔍 图标容器样式:', iconContainer.style.cssText );
				} else {
					// 如果符号不存在，使用备用图标
					console.warn( `⚠️ SVG符号不存在: ${spotlight.iconCode}，使用备用图标` );
					const fallbackIcon = createFallbackIcon( spotlight.iconCode, '16px', 'white' );
					iconContainer.appendChild( fallbackIcon );
				}
			} else {
				// 没有iconCode时显示默认图标
				console.log( '📝 亮点没有iconCode，使用默认图标' );
				const defaultIcon = createFallbackIcon( '', '16px', 'white' );
				iconContainer.appendChild( defaultIcon );
			}

			itemContainer.appendChild( iconContainer );

			const nameElement = document.createElement( 'div' );
			nameElement.className = 'spotlight-name';
			const fullName = spotlight.nameI18n?.zhContent || spotlight.name || '未命名';
			nameElement.textContent = fullName;
			nameElement.style.flex = '1';
			nameElement.style.fontWeight = 'bold';
			nameElement.style.fontSize = '12px';
			nameElement.style.overflow = 'hidden';
			nameElement.style.textOverflow = 'ellipsis';
			nameElement.style.whiteSpace = 'nowrap';

			// 添加鼠标悬浮显示完整名称
			nameElement.title = fullName;

			itemContainer.appendChild( nameElement );

			const typeElement = document.createElement( 'div' );
			typeElement.className = 'spotlight-type';
			typeElement.textContent = getSpotlightTypeLabel( spotlight.dataType );
			typeElement.style.width = '50px';
			typeElement.style.color = '#666';
			typeElement.style.fontSize = '11px';
			typeElement.style.marginRight = '8px';
			itemContainer.appendChild( typeElement );

			const addButton = document.createElement( 'button' );
			addButton.className = 'spotlight-add-btn';
			addButton.textContent = '添加';
			addButton.style.fontSize = '11px';
			addButton.style.padding = '2px 6px';
			addButton.onclick = () => addSpotlightToScene( spotlight );
			itemContainer.appendChild( addButton );

			// 悬停效果
			itemContainer.addEventListener( 'mouseenter', function () {
				itemContainer.style.backgroundColor = '#f0f0f0';
			} );

			itemContainer.addEventListener( 'mouseleave', function () {
				itemContainer.style.backgroundColor = '';
			} );

			return itemContainer;
		}

		// 获取亮点类型标签
		function getSpotlightTypeLabel( dataType ) {
			const typeMap = {
				1: '文字',
				2: '语音',
				3: '图片',
				4: '视频',
				5: '看板',
				6: '富文本',
				7: '标识牌',
				8: '监控',
				9: '联网设备'
			};
			return typeMap[ dataType ] || '未知';
		}

		/**
		 * 创建亮点图标纹理
		 * 在Canvas上绘制圆角矩形背景和SVG图标
		 */
		function createSpotlightTexture( spotlight ) {
			const canvas = document.createElement( 'canvas' );
			const size = 256; // 纹理尺寸
			canvas.width = size;
			canvas.height = size;
			const ctx = canvas.getContext( '2d' );

			// 绘制圆角矩形背景 - 占满整个canvas
			const rectWidth = size;
			const rectHeight = size;
			const rectX = 0;
			const rectY = 0;
			const cornerRadius = 16; // 适中的圆角半径

			// 创建圆角矩形路径
			ctx.beginPath();
			ctx.moveTo( rectX + cornerRadius, rectY );
			ctx.lineTo( rectX + rectWidth - cornerRadius, rectY );
			ctx.quadraticCurveTo( rectX + rectWidth, rectY, rectX + rectWidth, rectY + cornerRadius );
			ctx.lineTo( rectX + rectWidth, rectY + rectHeight - cornerRadius );
			ctx.quadraticCurveTo( rectX + rectWidth, rectY + rectHeight, rectX + rectWidth - cornerRadius, rectY + rectHeight );
			ctx.lineTo( rectX + cornerRadius, rectY + rectHeight );
			ctx.quadraticCurveTo( rectX, rectY + rectHeight, rectX, rectY + rectHeight - cornerRadius );
			ctx.lineTo( rectX, rectY + cornerRadius );
			ctx.quadraticCurveTo( rectX, rectY, rectX + cornerRadius, rectY );
			ctx.closePath();

			// 填充背景色
			ctx.fillStyle = spotlight.iconColor || '#b2c2c2';
			ctx.fill();

			// 添加边框
			ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
			ctx.lineWidth = 2;
			ctx.stroke();

			// 绘制图标 - 让图标占满整个canvas区域
			if ( spotlight.iconCode ) {
				// 查找SVG符号
				const symbol = document.querySelector( `#icon-${spotlight.iconCode}` );
				if ( symbol ) {
					console.log( `🎨 找到SVG符号: ${spotlight.iconCode}` );

					// 尝试使用Path2D绘制SVG路径
					try {
						const pathElements = symbol.querySelectorAll( 'path' );
						if ( pathElements.length > 0 ) {
							ctx.fillStyle = 'white';
							ctx.save();

							// 计算所有路径的实际边界框
							let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
							const paths = [];

							pathElements.forEach( pathElement => {
								const pathData = pathElement.getAttribute( 'd' );
								if ( pathData ) {
									const path2D = new Path2D( pathData );
									paths.push( path2D );

									// 创建临时canvas来测量路径边界
									const tempCanvas = document.createElement('canvas');
									tempCanvas.width = 1024;
									tempCanvas.height = 1024;
									const tempCtx = tempCanvas.getContext('2d');
									tempCtx.fillStyle = 'black';
									tempCtx.fill(path2D);

									// 获取ImageData来计算实际边界
									const imageData = tempCtx.getImageData(0, 0, 1024, 1024);
									const data = imageData.data;

									for (let y = 0; y < 1024; y++) {
										for (let x = 0; x < 1024; x++) {
											const index = (y * 1024 + x) * 4;
											if (data[index + 3] > 0) { // alpha > 0，说明有内容
												minX = Math.min(minX, x);
												minY = Math.min(minY, y);
												maxX = Math.max(maxX, x);
												maxY = Math.max(maxY, y);
											}
										}
									}
								}
							});

							// 如果找到了有效边界
							if (minX !== Infinity && minY !== Infinity && maxX !== -Infinity && maxY !== -Infinity) {
								const pathWidth = maxX - minX;
								const pathHeight = maxY - minY;
								const pathCenterX = minX + pathWidth / 2;
								const pathCenterY = minY + pathHeight / 2;

								// 计算缩放比例，让实际图标内容占满canvas的大部分
								const iconPadding = size * 0.1; // 10%边距
								const availableSize = size - iconPadding * 2;
								const scaleX = availableSize / pathWidth;
								const scaleY = availableSize / pathHeight;
								const scale = Math.min(scaleX, scaleY); // 保持宽高比

								// 移动到canvas中心
								ctx.translate(size / 2, size / 2);
								// 缩放
								ctx.scale(scale, scale);
								// 移动到路径的中心点
								ctx.translate(-pathCenterX, -pathCenterY);

								console.log( `🎨 智能缩放: pathBounds=[${minX},${minY},${maxX},${maxY}], pathSize=[${pathWidth},${pathHeight}], scale=${scale}` );
							} else {
								// 回退到原来的方法
								const iconPadding = size * 0.05;
								const iconSize = size - iconPadding * 2;
								const scale = iconSize / 1024;
								ctx.translate(size / 2, size / 2);
								ctx.scale(scale, scale);
								ctx.translate(-512, -512);
								console.log( `🎨 回退缩放: scale=${scale}` );
							}

							// 绘制所有路径
							paths.forEach( path2D => {
								ctx.fill( path2D );
							});

							ctx.restore();
							console.log( `✅ 使用智能缩放绘制图标: ${spotlight.iconCode}` );
						} else {
							throw new Error( '没有找到path元素' );
						}
					} catch ( error ) {
						console.warn( `⚠️ Path2D绘制失败: ${error.message}，使用文字备用` );
						// 备用方案：绘制大文字
						ctx.fillStyle = 'white';
						const fontSize = Math.floor(size * 0.8); // 字体大小为canvas的80%，更大更清晰
						ctx.font = `bold ${fontSize}px Arial`;
						ctx.textAlign = 'center';
						ctx.textBaseline = 'middle';
						const text = spotlight.iconCode.charAt(0).toUpperCase();
						ctx.fillText( text, size / 2, size / 2 );
						console.log( `📝 绘制备用文字: ${text}, fontSize=${fontSize}` );
					}
				} else {
					console.warn( `⚠️ 未找到SVG符号: ${spotlight.iconCode}，使用文字备用` );
					// 如果没有找到SVG符号，绘制大文字
					ctx.fillStyle = 'white';
					const fontSize = Math.floor(size * 0.8); // 字体大小为canvas的80%，更大更清晰
					ctx.font = `bold ${fontSize}px Arial`;
					ctx.textAlign = 'center';
					ctx.textBaseline = 'middle';
					const text = spotlight.iconCode.charAt(0).toUpperCase();
					ctx.fillText( text, size / 2, size / 2 );
				}
			} else {
				// 没有图标代码时绘制默认符号
				ctx.fillStyle = 'white';
				const fontSize = Math.floor(size * 0.8); // 字体大小为canvas的80%，更大更清晰
				ctx.font = `bold ${fontSize}px Arial`;
				ctx.textAlign = 'center';
				ctx.textBaseline = 'middle';
				ctx.fillText( '●', size / 2, size / 2 );
			}

			return canvas;
		}

		// 添加亮点到场景
		function addSpotlightToScene( spotlight ) {
			try {
				console.log( '🎯 开始创建亮点精灵:', spotlight );

				// 创建Canvas纹理
				const canvas = createSpotlightTexture( spotlight );
				const texture = new THREE.CanvasTexture( canvas );
				texture.needsUpdate = true;
				canvas._texture = texture; // 保存引用以便后续更新

				// 创建精灵材质
				const material = new THREE.SpriteMaterial( {
					map: texture,
					transparent: true,
					alphaTest: 0.1
				} );

				// 创建精灵
				const sprite = new THREE.Sprite( material );
				sprite.scale.set( 1.2, 1.2, 1 ); // 适中的精灵大小，既清晰可见又不会太大

				sprite.name = spotlight.nameI18n?.zhContent || spotlight.name || '亮点';
				sprite.userData = {
					type: 'spotlight',
					spotlightData: spotlight
				};

				// 设置随机位置
				sprite.position.set(
					Math.random() * 10 - 5,
					Math.random() * 10 - 5,
					Math.random() * 10 - 5
				);

				// 添加到场景
				editor.addObject( sprite );
				editor.select( sprite );

				console.log( '✅ 亮点精灵已添加到场景:', spotlight );
			} catch ( error ) {
				console.error( '❌ 添加亮点到场景失败:', error );
			}
		}

		// 搜索和筛选功能
		function filterSpotlights() {
			const searchTerm = searchInput.value.toLowerCase();
			const selectedType = typeSelect.value;
			const selectedFactory = factorySelect.value;

			const filteredData = externalSpotlightData.filter( spotlight => {
				const nameMatch = !searchTerm ||
					( spotlight.nameI18n?.zhContent || spotlight.name || '' ).toLowerCase().includes( searchTerm );
				const typeMatch = !selectedType || spotlight.dataType == selectedType;
				const factoryMatch = !selectedFactory || spotlight.factoryCode == selectedFactory;

				return nameMatch && typeMatch && factoryMatch;
			});

			renderSpotlightList( filteredData );
			statusContainer.textContent = `显示 ${filteredData.length} / ${externalSpotlightData.length} 个亮点`;
		}

		// 绑定事件
		searchInput.addEventListener( 'input', filterSpotlights );
		typeSelect.addEventListener( 'change', filterSpotlights );
		factorySelect.addEventListener( 'change', filterSpotlights );

		// 初始渲染
		renderSpotlightList();
	}

	/**
	 * 创建文件夹容器
	 * @param {string} title - 文件夹标题
	 * @param {Array} subCategories - 子分类数组
	 * @param {number} level - 层级深度
	 * @param {boolean} expanded - 是否展开
	 * @returns {HTMLElement} 文件夹DOM元素
	 */
	function createLibraryFolder( title, subCategories, level = 0, expanded = false ) {
		const folderContainer = document.createElement( 'div' );
		folderContainer.className = `library-folder level-${level}`;

		// 文件夹标题
		const folderHeader = document.createElement( 'div' );
		folderHeader.className = 'library-folder-header';
		folderContainer.appendChild( folderHeader );

		// 添加缩进
		const indent = document.createElement( 'span' );
		indent.className = 'folder-indent';
		indent.style.paddingLeft = `${level * 16}px`;
		folderHeader.appendChild( indent );

		const folderIcon = document.createElement( 'span' );
		folderIcon.className = 'library-folder-icon';
		folderIcon.textContent = expanded ? '📂' : '📁';
		folderHeader.appendChild( folderIcon );

		const folderTitle = document.createElement( 'span' );
		folderTitle.className = 'library-folder-title';
		folderTitle.textContent = title;
		folderHeader.appendChild( folderTitle );

		const folderToggle = document.createElement( 'span' );
		folderToggle.className = 'library-folder-toggle';
		folderToggle.textContent = expanded ? '▼' : '▶';
		folderHeader.appendChild( folderToggle );

		// 文件夹内容
		const folderContent = document.createElement( 'div' );
		folderContent.className = 'library-folder-content';
		folderContent.style.display = expanded ? 'block' : 'none';
		folderContainer.appendChild( folderContent );

		// 递归渲染子分类或显示空状态
		if ( subCategories && subCategories.length > 0 ) {
			renderNestedCategories( folderContent, subCategories, level + 1 );
		} else {
			// 显示空文件夹提示
			const emptyMessage = document.createElement( 'div' );
			emptyMessage.className = 'folder-empty-message';
			emptyMessage.innerHTML = `
				<div class="empty-icon">📁</div>
				<div class="empty-text">此文件夹暂无内容</div>
			`;
			folderContent.appendChild( emptyMessage );
		}

		// 点击展开/收缩
		folderHeader.addEventListener( 'click', function () {
			const isExpanded = folderContent.style.display === 'block';
			folderContent.style.display = isExpanded ? 'none' : 'block';
			folderToggle.textContent = isExpanded ? '▶' : '▼';
			folderIcon.textContent = isExpanded ? '📁' : '📂';
		} );

		return folderContainer;
	}

	/**
	 * 创建模型库分类
	 * @param {string} title - 分类标题
	 * @param {Array} models - 模型数组
	 * @param {boolean} expanded - 是否展开
	 * @returns {HTMLElement} 分类DOM元素
	 */
	function createLibraryCategory( title, models, expanded = false ) {

		const categoryContainer = document.createElement( 'div' );
		categoryContainer.className = 'library-category';

		// 分类标题
		const categoryHeader = document.createElement( 'div' );
		categoryHeader.className = 'library-category-header';
		categoryContainer.appendChild( categoryHeader );

		// 添加文件夹图标，与createLibraryFolder保持一致
		const categoryIcon = document.createElement( 'span' );
		categoryIcon.className = 'library-category-icon';
		categoryIcon.textContent = expanded ? '📂' : '📁';
		categoryHeader.appendChild( categoryIcon );

		const categoryTitle = document.createElement( 'span' );
		categoryTitle.className = 'library-category-title';
		categoryTitle.textContent = title;
		categoryHeader.appendChild( categoryTitle );

		const categoryToggle = document.createElement( 'span' );
		categoryToggle.className = 'library-category-toggle';
		categoryToggle.textContent = expanded ? '▼' : '▶';
		categoryHeader.appendChild( categoryToggle );

		// 分类内容
		const categoryContent = document.createElement( 'div' );
		categoryContent.className = 'library-category-content';
		categoryContent.style.display = expanded ? 'block' : 'none';
		categoryContainer.appendChild( categoryContent );

		// 添加模型项或空状态提示
		if ( models.length > 0 ) {
			const modelsGrid = document.createElement( 'div' );
			modelsGrid.className = 'library-models-grid';
			categoryContent.appendChild( modelsGrid );

			models.forEach( model => {
				const modelItem = createModelItem( model );
				modelsGrid.appendChild( modelItem );
			} );
		} else {
			// 显示空分类提示
			const emptyMessage = document.createElement( 'div' );
			emptyMessage.className = 'category-empty-message';
			emptyMessage.innerHTML = `
				<div class="empty-icon">📦</div>
				<div class="empty-text">此分类暂无模型</div>
			`;
			categoryContent.appendChild( emptyMessage );
		}

		// 点击展开/收缩
		categoryHeader.addEventListener( 'click', function () {
			const isExpanded = categoryContent.style.display !== 'none';
			categoryContent.style.display = isExpanded ? 'none' : 'block';
			categoryToggle.textContent = isExpanded ? '▶' : '▼';
			categoryIcon.textContent = isExpanded ? '📁' : '📂'; // 同时更新文件夹图标
		} );

		return categoryContainer;

	}

	/**
	 * 创建资源项
	 * 创建单个可拖拽的资源项元素
	 *
	 * @param {Object} item - 资源项数据
	 * @returns {HTMLElement} 创建的资源项DOM元素
	 */
	function createResourceItem( item ) {

		// 创建资源项容器
		const resourceItem = document.createElement( 'div' );
		resourceItem.className = 'resource-item';

		// 上传功能不需要拖拽，其他资源启用拖拽
		if ( item.type !== 'upload' ) {
			resourceItem.draggable = true;
			resourceItem.title = `Drag to add ${item.label} to scene`;
		} else {
			resourceItem.title = `Click to upload 3D model files`;
		}

		// 设置数据属性
		resourceItem.dataset.type = item.type;
		resourceItem.dataset.data = JSON.stringify( item.data );

		// 创建图标
		const icon = document.createElement( 'div' );
		icon.className = 'icon';
		icon.innerHTML = item.icon;
		resourceItem.appendChild( icon );

		// 创建标签
		const label = document.createElement( 'div' );
		label.className = 'label';
		label.textContent = item.label;
		resourceItem.appendChild( label );

		// 处理上传功能
		if ( item.type === 'upload' ) {

			setupUploadItem( resourceItem );

		} else {

			setupDraggableItem( resourceItem, item );

		}

		return resourceItem;

	}

	/**
	 * 设置上传功能的资源项
	 * 为上传类型的资源项添加文件选择功能
	 *
	 * @param {HTMLElement} resourceItem - 资源项DOM元素
	 */
	function setupUploadItem( resourceItem ) {

		// 创建隐藏的文件输入元素
		const fileInput = document.createElement( 'input' );
		fileInput.type = 'file';
		fileInput.multiple = true;
		fileInput.accept = '.gltf,.glb,.fbx,.obj,.dae,.3ds,.ply,.stl,.json';
		fileInput.style.display = 'none';

		// 文件选择事件处理
		fileInput.addEventListener( 'change', function () {

			if ( fileInput.files.length > 0 ) {
				try {
					// 显示上传开始通知
					const fileCount = fileInput.files.length;
					const fileNames = Array.from( fileInput.files ).map( f => f.name ).join( ', ' );
					showNotification( `Uploading ${fileCount} file(s): ${fileNames}` );

					// 使用编辑器的加载器加载文件
					editor.loader.loadFiles( fileInput.files );

					// 重置文件输入
					fileInput.value = '';
				} catch ( error ) {
					console.error( 'Error loading files:', error );
					showNotification( 'Error: Failed to load files' );
					// 确保重置文件输入
					fileInput.value = '';
				}
			}

		} );

		// 点击事件处理 - 触发文件选择
		resourceItem.addEventListener( 'click', function () {

			try {
				fileInput.click();
			} catch ( error ) {
				console.error( 'Error opening file dialog:', error );
				showNotification( 'Error: Unable to open file dialog' );
			}

		} );

		// 将文件输入添加到DOM
		document.body.appendChild( fileInput );

	}

	/**
	 * 设置可拖拽的资源项
	 * 为非上传类型的资源项添加拖拽功能
	 *
	 * @param {HTMLElement} resourceItem - 资源项DOM元素
	 * @param {Object} item - 资源项数据
	 */
	function setupDraggableItem( resourceItem, item ) {

		// 拖拽开始事件处理
		resourceItem.addEventListener( 'dragstart', function ( event ) {

			// 设置拖拽数据，使用JSON格式传递资源信息
			event.dataTransfer.setData( 'application/json', JSON.stringify( {
				type: item.type,    // 资源类型（geometry、light等）
				data: item.data     // 资源具体数据
			} ) );

			// 设置拖拽效果为复制
			event.dataTransfer.effectAllowed = 'copy';

			// 添加拖拽样式
			resourceItem.classList.add( 'dragging' );

		} );

		resourceItem.addEventListener( 'dragend', function ( event ) {

			// 移除拖拽样式
			resourceItem.classList.remove( 'dragging' );

		} );

	}

	// 定义资源分类数据
	const categories = [
		{
			id: 'geometry',
			title: 'Geometry',
			icon: '📐',
			items: [
				{ type: 'geometry', data: { type: 'BoxGeometry', args: [ 1, 1, 1 ] }, icon: '⬜', label: 'Box' },
				{ type: 'geometry', data: { type: 'SphereGeometry', args: [ 0.5, 32, 16 ] }, icon: '⚪', label: 'Sphere' },
				{ type: 'geometry', data: { type: 'CylinderGeometry', args: [ 0.5, 0.5, 1, 32 ] }, icon: '🔵', label: 'Cylinder' },
				{ type: 'geometry', data: { type: 'PlaneGeometry', args: [ 1, 1 ] }, icon: '▭', label: 'Plane' },
				{ type: 'geometry', data: { type: 'ConeGeometry', args: [ 0.5, 1, 32 ] }, icon: '🔺', label: 'Cone' },
				{ type: 'geometry', data: { type: 'TorusGeometry', args: [ 0.4, 0.1, 16, 100 ] }, icon: '⭕', label: 'Torus' },
				{ type: 'geometry', data: { type: 'DodecahedronGeometry', args: [ 0.5 ] }, icon: '⬟', label: 'Dodeca' },
				{ type: 'geometry', data: { type: 'IcosahedronGeometry', args: [ 0.5 ] }, icon: '🔷', label: 'Icosa' }
			]
		},
		{
			id: 'lights',
			title: 'Lights',
			icon: '💡',
			items: [
				{ type: 'light', data: { type: 'DirectionalLight', args: [ 0xffffff, 1 ] }, icon: '☀️', label: 'Directional' },
				{ type: 'light', data: { type: 'PointLight', args: [ 0xffffff, 1, 100 ] }, icon: '💡', label: 'Point' },
				{ type: 'light', data: { type: 'SpotLight', args: [ 0xffffff, 1, 100, Math.PI / 4 ] }, icon: '🔦', label: 'Spot' },
				{ type: 'light', data: { type: 'AmbientLight', args: [ 0x404040, 0.4 ] }, icon: '🌙', label: 'Ambient' },
				{ type: 'light', data: { type: 'HemisphereLight', args: [ 0xffffbb, 0x080820, 1 ] }, icon: '🌅', label: 'Hemisphere' }
			]
		},
		{
			id: 'models',
			title: 'Models',
			icon: '📦',
			items: [
				{ type: 'upload', data: {}, icon: '📁', label: 'Upload Model' },
				{ type: 'model', data: { url: 'models/cube.glb' }, icon: '📦', label: 'Cube Model' },
				{ type: 'model', data: { url: 'models/sphere.glb' }, icon: '🏀', label: 'Sphere Model' }
			]
		},
		{
			id: 'modelLibrary',
			title: 'Model Library',
			icon: '🏛️',
			isSpecial: true, // 标记为特殊分类，使用不同的显示方式
			items: [] // 模型库不使用普通的items结构
		},
		{
			id: 'spotlight',
			title: 'Spotlight',
			icon: '🌟',
			isSpecial: true, // 标记为特殊分类，使用自定义界面
			items: [] // 亮点不使用普通的items结构
		}
	];

	// 创建分类按钮
	categories.forEach( category => {

		const button = createCategoryButton( category.id, category.title, category.icon, category.items );
		categoryButtons.add( button );

	} );

	// 添加logo到最下方
	const logoContainer = new UIDiv();
	logoContainer.setClass( 'logo-container' );

	const logoImg = document.createElement( 'img' );
	logoImg.src = '../../../src/assets/logo/logo30-w.svg'; // 使用白色logo适配深色主题
	logoImg.alt = 'Logo';
	logoImg.className = 'logo-image';

	logoContainer.dom.appendChild( logoImg );
	categoryButtons.add( logoContainer );

	// Handle drop events on viewport
	function setupDropZone() {

		const viewport = document.getElementById( 'viewport' );

		if ( !viewport ) return;

		let dragOverlay = null;

		viewport.addEventListener( 'dragenter', function ( event ) {

			event.preventDefault();

			// Create drag overlay for visual feedback
			if ( !dragOverlay ) {
				dragOverlay = document.createElement( 'div' );
				dragOverlay.style.position = 'absolute';
				dragOverlay.style.top = '0';
				dragOverlay.style.left = '0';
				dragOverlay.style.right = '0';
				dragOverlay.style.bottom = '0';
				dragOverlay.style.backgroundColor = 'rgba(0, 136, 255, 0.1)';
				dragOverlay.style.border = '2px dashed #08f';
				dragOverlay.style.pointerEvents = 'none';
				dragOverlay.style.zIndex = '1000';
				viewport.appendChild( dragOverlay );
			}

		} );

		viewport.addEventListener( 'dragover', function ( event ) {

			event.preventDefault();
			event.dataTransfer.dropEffect = 'copy';

		} );

		viewport.addEventListener( 'dragleave', function ( event ) {

			// Remove overlay when leaving viewport
			if ( event.target === viewport && dragOverlay ) {
				viewport.removeChild( dragOverlay );
				dragOverlay = null;
			}

		} );

		viewport.addEventListener( 'drop', function ( event ) {

			event.preventDefault();

			// Remove drag overlay
			if ( dragOverlay ) {
				viewport.removeChild( dragOverlay );
				dragOverlay = null;
			}

			try {

				const data = JSON.parse( event.dataTransfer.getData( 'application/json' ) );

				if ( data.type === 'geometry' ) {

					createGeometryObject( data.data, event );

				} else if ( data.type === 'light' ) {

					createLightObject( data.data, event );

				} else if ( data.type === 'model' ) {

					createModelObject( data.data, event );

				}

			} catch ( error ) {

				console.error( 'Error parsing drop data:', error );

			}

		} );

	}

	function createGeometryObject( geometryData, event ) {

		const geometry = new THREE[ geometryData.type ]( ...geometryData.args );
		const material = new THREE.MeshStandardMaterial( { color: 0x888888 } );
		const mesh = new THREE.Mesh( geometry, material );

		// Position using raycasting for more accurate placement
		const position = getDropPosition( event );
		mesh.position.copy( position );
		mesh.name = geometryData.type.replace( 'Geometry', '' );

		// Use editor's addObject method instead of command
		editor.addObject( mesh );
		editor.select( mesh );

		// Show notification
		showNotification( `Added ${mesh.name} to scene` );

	}

	function createLightObject( lightData, event ) {

		const light = new THREE[ lightData.type ]( ...lightData.args );

		// Position lights based on type
		const position = getDropPosition( event );

		if ( light.type === 'DirectionalLight' ) {
			light.position.set( position.x + 5, position.y + 5, position.z + 5 );
			light.target.position.copy( position );
		} else if ( light.type === 'AmbientLight' || light.type === 'HemisphereLight' ) {
			light.position.set( 0, 0, 0 ); // These lights don't need specific positioning
		} else {
			light.position.set( position.x, position.y + 3, position.z );
		}

		light.name = lightData.type;

		// Use editor's addObject method instead of command
		editor.addObject( light );
		editor.select( light );

		// Show notification
		showNotification( `Added ${light.name} to scene` );

	}

	// 构建平台API的完整URL
	function buildPlatformApiUrl(relativePath) {
		if (!relativePath) {
			return '';
		}

		// 如果已经是完整URL，直接返回
		if (relativePath.startsWith('http://') || relativePath.startsWith('https://')) {
			return relativePath;
		}

		// 获取当前项目的origin（协议+域名+端口）
		const origin = window.location.origin;

		// 确保相对路径以正确的格式开始
		const cleanPath = relativePath.startsWith('/') ? relativePath.substring(1) : relativePath;

		// 拼接完整URL
		return `${origin}/platform-api/${cleanPath}`;
	}

	function createModelObject( modelData, event ) {

		const originalUrl = modelData.url || '';
		const url = buildPlatformApiUrl(originalUrl);
		const name = modelData.name;

		console.log( `[DragDrop] 1. Attempting to load model: ${name}` );
		console.log( `[DragDrop] 1.1. Original URL: ${originalUrl}` );
		console.log( `[DragDrop] 1.2. Processed URL: ${url}` );
		console.log( `[DragDrop] 2. Model data:`, modelData );

		if ( ! modelData.url ) {
			console.error( '[DragDrop] ERROR: Model data does not contain a URL.', modelData );
			showNotification( `Error: Model ${ name } has no URL.`, 'error' );
			return;
		}

		showNotification( `Loading ${ name }...` );

		const extension = url.split( '.' ).pop().toLowerCase();
		let loader;

		// 根据文件扩展名选择合适的加载器
		// 这些加载器实例是在Editor.js -> Loader.js中创建并挂载到editor.loader上的
		switch ( extension ) {

			case 'glb':
			case 'gltf':
				loader = editor.loader.gltfLoader;
				break;

			case 'fbx':
				loader = editor.loader.fbxLoader;
				break;

			case 'obj':
				loader = editor.loader.objLoader;
				break;

			default:
				showNotification( `Unsupported file format: .${ extension }`, 'error' );
				console.error( `[DragDrop] ERROR: No loader for extension: ${ extension }` );
				return;

		}

		console.log( `[DragDrop] 3. Selected loader for .${extension}:`, loader );

		if ( ! loader ) {
			showNotification( 'Loader not available for this file type.', 'error' );
			console.error( `[DragDrop] ERROR: Loader not available for .${extension}` );
			return;
		}

		console.log( '[DragDrop] 4. loader.load() has been called. Waiting for async callback.' );

		// 调用加载器的 .load 方法，它是异步的
		loader.load( url, function ( loadedObject ) {

			console.log( '[DragDrop] 5. Model loaded successfully from network.', loadedObject );

			// 不同加载器返回的对象结构不同，GLTF返回一个包含scene属性的对象，其他可能直接返回object
			const object = loadedObject.scene || loadedObject;

			console.log( '[DragDrop] 6. Extracted scene object:', object );

			// 设置模型的名称
			object.name = name;

			// 使用光线投射计算精确的放置位置
			const position = getDropPosition( event );
			console.log( '[DragDrop] 7. Calculated drop position:', position );
			object.position.copy( position );

			// 使用编辑器命令添加对象，以便支持撤销/重做
			console.log( '[DragDrop] 8. Adding object to scene via command...' );
			editor.execute( new AddObjectCommand( editor, object ) );

			showNotification( `Added ${ name } to scene`, 'success' );
			console.log( `[DragDrop] 9. SUCCESS: Added ${name} to scene.` );

		}, undefined, function ( error ) {

			const message = `Failed to load model: ${ name }`;
			console.error( '[DragDrop] ERROR: Failed to load model from network.', error );
			showNotification( `${ message }. See console for details.`, 'error' );

		} );

	}

	function getDropPosition( event ) {

		const viewport = document.getElementById( 'viewport' );
		const rect = viewport.getBoundingClientRect();

		// Convert mouse position to normalized device coordinates
		const mouse = new THREE.Vector2();
		mouse.x = ( ( event.clientX - rect.left ) / rect.width ) * 2 - 1;
		mouse.y = - ( ( event.clientY - rect.top ) / rect.height ) * 2 + 1;

		// Create raycaster
		const raycaster = new THREE.Raycaster();
		raycaster.setFromCamera( mouse, editor.camera );

		// Try to intersect with existing objects
		const intersects = raycaster.intersectObjects( editor.scene.children, true );

		if ( intersects.length > 0 ) {
			// Place on the surface of the intersected object
			return intersects[ 0 ].point;
		} else {
			// Place on a virtual ground plane
			const groundPlane = new THREE.Plane( new THREE.Vector3( 0, 1, 0 ), 0 );
			const target = new THREE.Vector3();
			raycaster.ray.intersectPlane( groundPlane, target );
			return target;
		}

	}

	// Initialize drop zone when viewport is ready
	setTimeout( setupDropZone, 100 );

	// Add keyboard shortcut to toggle panel (Ctrl+R)
	document.addEventListener( 'keydown', function ( event ) {

		if ( event.ctrlKey && event.key === 'r' ) {
			event.preventDefault();
			toggleBtn.dom.click();
		}

	} );

	function showNotification( message ) {

		// Create notification element
		const notification = document.createElement( 'div' );
		notification.style.position = 'fixed';
		notification.style.top = '50px';
		notification.style.right = '20px';
		notification.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
		notification.style.color = 'white';
		notification.style.padding = '12px 20px';
		notification.style.borderRadius = '6px';
		notification.style.boxShadow = '0 4px 12px rgba(0,0,0,0.2)';
		notification.style.zIndex = '10000';
		notification.style.fontSize = '14px';
		notification.style.fontWeight = 'bold';
		notification.style.opacity = '0';
		notification.style.transform = 'translateX(100%)';
		notification.style.transition = 'all 0.3s ease';
		notification.textContent = message;

		document.body.appendChild( notification );

		// Animate in
		setTimeout( () => {
			notification.style.opacity = '1';
			notification.style.transform = 'translateX(0)';
		}, 10 );

		// Animate out and remove
		setTimeout( () => {
			notification.style.opacity = '0';
			notification.style.transform = 'translateX(100%)';
			setTimeout( () => {
				if ( notification.parentNode ) {
					document.body.removeChild( notification );
				}
			}, 300 );
		}, 2000 );

	}

	// 暴露设置模型库数据的方法
	container.setModelLibraryData = function( data ) {
		externalModelLibraryData = data;
		console.log('模型库数据已更新:', data);

		// 如果当前正在显示模型库，刷新显示
		const libraryContainer = detailContent.dom.querySelector('.model-library-container');
		if ( libraryContainer ) {
			// 查找系统和用户内容容器
			const systemContent = libraryContainer.querySelector('.system-content');
			const userContent = libraryContainer.querySelector('.user-content');

			if ( systemContent ) {
				// 清空并重新渲染系统模型库
				systemContent.innerHTML = '';
				renderSystemLibrary( systemContent );
			}

			if ( userContent ) {
				// 清空并重新渲染用户模型库
				userContent.innerHTML = '';
				renderUserLibrary( userContent );
			}

			console.log('模型库内容已刷新');
		}
	};

	// 暴露设置亮点数据的方法
	container.setSpotlightData = function( data ) {
		externalSpotlightData = data || [];
		console.log('亮点数据已更新:', data);

		// 如果当前正在显示亮点库，刷新显示
		const spotlightContainer = detailContent.dom.querySelector('.spotlight-library-container');
		if ( spotlightContainer ) {
			// 重新显示亮点库
			detailContent.dom.innerHTML = '';
			showSpotlightLibrary();
			console.log('亮点库内容已刷新');
		}
	};

	// 暴露设置工厂数据的方法
	container.setFactoryData = function( data ) {
		externalFactoryData = data || [];
		console.log('工厂数据已更新:', data);

		// 如果当前正在显示亮点库，刷新显示以更新工厂筛选选项
		const spotlightContainer = detailContent.dom.querySelector('.spotlight-library-container');
		if ( spotlightContainer ) {
			// 重新显示亮点库
			detailContent.dom.innerHTML = '';
			showSpotlightLibrary();
			console.log('亮点库内容已刷新（工厂数据更新）');
		}
	};

	return container;

}

export { ResourcePanel };
