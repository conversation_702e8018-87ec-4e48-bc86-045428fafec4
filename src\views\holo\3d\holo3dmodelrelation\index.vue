<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <el-form v-show="showSearch" :inline="true" :model="queryParams" class="search-bar">
      <el-form-item label="工厂" :style="{ width: '200px' }">
        <el-select v-model="queryParams.factoryCode" placeholder="请选择工厂" clearable @change="handleFactoryChange">
          <el-option v-for="item in factoryOptions" :key="item.code" :label="item.name" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item label="区域" :style="{ width: '200px' }">
        <el-select v-model="queryParams.regionCode" placeholder="请选择区域" clearable @change="handleRegionChange">
          <el-option v-for="item in regionOptions" :key="item.code" :label="item.name" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item label="楼栋" :style="{ width: '200px' }">
        <el-select
          v-model="queryParams.buildingCode"
          placeholder="请选择楼栋"
          clearable
          :disabled="queryParams.regionCode === '0'"
          @change="handleBuildingChange"
        >
          <el-option v-for="item in buildingOptions" :key="item.code" :label="item.name" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item label="楼层" :style="{ width: '200px' }">
        <el-select
          v-model="queryParams.floorCode"
          placeholder="请选择楼层"
          clearable
          :disabled="queryParams.regionCode === '0'"
        >
          <el-option v-for="item in floorOptions" :key="item.code" :label="item.name" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item label="模型名称">
        <el-input v-model="queryParams.name" placeholder="请输入模型名称" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <!-- 卡片视图 -->
    <el-row :gutter="20">
      <!-- 左侧模型卡片列表 -->
      <el-col :span="5" style="padding: 10px; height: 85vh; box-shadow: var(--el-box-shadow)">
        <el-scrollbar wrap-style="overflow-y: auto;" view-style="scrollbar-width: none;">
          <div style="color: #f00000">右键设置首图</div>
          <div class="card-container">
            <div style="display: flex; flex-direction: column; align-items: center; width: 100%">
              <div
                v-for="item in modelList"
                :key="item.id"
                class="card-item"
                style="cursor: pointer"
                @click="selectModel(item)"
              >
                <div class="model-card"  :class="{ 'is-active': activeModel?.id === item.id }">
                  <div class="card-wrapper">
                    <div class="card-image-container" @contextmenu.prevent="showContextMenu($event, item)">
                      <!--                      <el-tag type="danger" style="position: absolute; z-index: 9; top: 5px; left: 5px"
                      >{{
                          item.startStatus == 1?'首图':'非首图'
                        }}</el-tag>-->
                      <el-tag
                        v-if="item.startStatus == 1"
                        type="success"
                        style="position: absolute; z-index: 9; top: 5px; left: 5px"
                        >首图</el-tag
                      >
                      <el-tag v-else type="danger" style="position: absolute; z-index: 9; top: 5px; left: 5px"
                        >非首图</el-tag
                      >

                      <el-tag type="success" style="position: absolute; z-index: 9; top: 5px; right: 5px">{{
                        item.relationCount
                      }}</el-tag>
                      <model-viewer
                        v-if="item.objectUrl"
                        :src="'/platform-api/' + item.objectUrl"
                        alt="3D 模型"
                        class="model-viewer"
                        auto-rotate
                        camera-controls
                        disable-zoom
                        touch-action="pan-y"
                        exposure="1.0"
                        :style="{ backgroundColor: item.hover ? '#ffffff' : '#182f4a' }"
                      ></model-viewer>
                      <div v-else class="card-image-placeholder">
                        <el-icon :size="60" color="#c0c4cc">
                          <Box />
                        </el-icon>
                      </div>

                      <!-- 右键菜单 -->
                      <div
                        v-show="contextMenuVisible && activeContextItem == item"
                        class="context-menu"
                        :style="{ top: `${menuPosition.y}px`, left: `${menuPosition.x}px` }"
                      >
                        <el-button @click.stop="configFirstModel"> 设置首图 </el-button>
                      </div>
                    </div>
                    <div class="card-content">
                      <el-tooltip :content="item.name" placement="top" :show-after="500">
                        <div class="card-title">{{ item.name || '未命名模型' }}</div>
                      </el-tooltip>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-scrollbar>
      </el-col>

      <!-- 右侧 G6 显示区域 -->
      <el-col :span="18" style="margin-left: 10px; padding: 10px; height: 85vh; box-shadow: var(--el-box-shadow)">
        <div v-if="!activeModel">
          <el-empty description="选择左方模型进入，检视/编辑关联" />
        </div>
        <div v-else style="height: 100%">
          <div style="font-weight: bold; margin-bottom: 10px">模型名称：{{ activeModel.name }}</div>
          <!-- G6 容器 -->
          <!--          <div id="g6-container" style="width: 100%; height: calc(100% - 30px)"></div>-->
          <DecisionTreeGraph :selectModel="activeModel" @initList="getList" :modelList="modelList"/>
        </div>
      </el-col>
    </el-row>

    <!-- 添加或修改业务 holo工厂3D模型对话框 -->
    <el-dialog v-model="open" :title="title" width="50vw" append-to-body>
      <el-form ref="modelRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="工厂" prop="factoryCode">
          <el-select
            v-model="form.factoryCode"
            placeholder="请选择工厂"
            clearable
            style="width: 100%"
            @change="handleFormFactoryChange"
          >
            <el-option v-for="item in factoryOptions" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>
        <el-form-item label="区域" prop="regionCode">
          <el-select
            v-model="form.regionCode"
            placeholder="请选择区域"
            clearable
            style="width: 100%"
            @change="handleFormRegionChange"
          >
            <el-option v-for="item in formRegionOptions" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>
        <el-form-item label="楼栋" prop="buildingCode">
          <el-select
            v-model="form.buildingCode"
            placeholder="请选择楼栋"
            clearable
            style="width: 100%"
            :disabled="form.regionCode === '0'"
            @change="handleFormBuildingChange"
          >
            <el-option v-for="item in formBuildingOptions" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>
        <el-form-item label="楼层" prop="floorCode">
          <el-select
            v-model="form.floorCode"
            placeholder="请选择楼层"
            clearable
            style="width: 100%"
            :disabled="form.regionCode === '0'"
          >
            <el-option v-for="item in formFloorOptions" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>
        <el-form-item label="模型名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入模型名称" />
        </el-form-item>
        <el-form-item label="模型描述" prop="desc">
          <el-input v-model="form.desc" placeholder="请输入模型描述" />
        </el-form-item>
        <el-form-item label="模型类型" prop="isSystem">
          <el-select v-model="form.isSystem" placeholder="请选择模型类型" style="width: 100%">
            <el-option label="用户模型" :value="0" />
            <el-option label="系统模型" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="文件后缀" prop="suffix">
          <el-select v-model="form.suffix" placeholder="请选择文件后缀" style="width: 100%">
            <el-option label="GLB" value="glb" />
            <el-option label="GLTF" value="gltf" />
            <el-option label="FBX" value="fbx" />
            <el-option label="OBJ" value="obj" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否首图" prop="startStatus">
          <el-switch
            v-model="form.startStatus"
            :active-value="1"
            :inactive-value="0"
            active-text="是"
            inactive-text="否"
          />
        </el-form-item>
        <el-form-item label="公版类型" prop="isCommon">
          <el-select v-model="form.isCommon" placeholder="请选择公版类型" style="width: 100%">
            <el-option label="用户上传" :value="0" />
            <el-option label="用户配置公版" :value="1" />
            <el-option label="用户配置非公版" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="模型文件" prop="objCode">
          <file-upload v-model="form.objCode" />
        </el-form-item>
        <el-form-item label="模型缩略图" prop="modelImg">
          <image-upload v-model="form.modelImg" />
        </el-form-item>
        <el-form-item label="备注信息" prop="remark">
          <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="holo3dmodelrelation">
import { getCurrentInstance, watch, onMounted, onActivated, nextTick, onBeforeUnmount } from 'vue';
import { useRoute } from 'vue-router';
import { listModel, getModel, delModel, addModel, updateModel, SetStartStatus } from '@/api/holo/model';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Graph } from '@antv/g6';
import { Box } from '@element-plus/icons-vue';
import Pagination from '@/components/Pagination/index';
import { getUserFactory } from '@/api/holo/factory';
import { getRegionList, findBuildingListWithFloor, getRegion } from '@/api/floor';
import DecisionTreeGraph from './DecisionTreeGraph.vue';

const { proxy } = getCurrentInstance();
const route = useRoute();
const QUERY_CACHE_KEY = 'model_query_cache';
const activeModel = ref(null);
const modelList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');
const contextMenuVisible = ref(false);
const menuPosition = ref({ x: 0, y: 0 });
const activeContextItem = ref(null);

// 新增的数据
const factoryOptions = ref([]);
const regionOptions = ref([]);
const buildingOptions = ref([]);
const floorOptions = ref([]);

// 弹窗专用的选项数据
const formRegionOptions = ref([]);
const formBuildingOptions = ref([]);
const formFloorOptions = ref([]);

const viewMode = ref('table'); // 视图模式：table-表格，card-卡片

const queryParams = ref({
  page: 1,
  size: 20,
  factoryCode: '',
  regionCode: '',
  buildingCode: '',
  floorCode: '',
  name: '',
  orderNum: '',
  orderByColumn: 'id', // 排序字段
  isAsc: 'asc', // 排序方式
  isSystem: 0,
  isCommonList: [1, 2].join(','),
});

const data = reactive({
  form: {},
  rules: {
    groupCode: [{ required: true, message: '集团code不能为空', trigger: 'blur' }],
    organizeCode: [{ required: true, message: '组织code不能为空', trigger: 'blur' }],
    objCode: [{ required: true, message: '对象存储code不能为空', trigger: 'blur' }],
    code: [{ required: true, message: '表code不能为空', trigger: 'blur' }],
    name: [{ required: true, message: '模型名称不能为空', trigger: 'blur' }],
    factoryCode: [{ required: true, message: '工厂code不能为空', trigger: 'blur' }],
    isSystem: [{ required: true, message: '0为用户上传1为系统模型不能为空', trigger: 'blur' }],
    startStatus: [{ required: true, message: '工厂首个展示的模型0：否 1：是不能为空', trigger: 'change' }],
    isCommon: [{ required: true, message: '0用户上传1为用户配置好的公版2用户配置的非公版不能为空', trigger: 'blur' }],
    suffix: [{ required: true, message: '文件后缀不能为空', trigger: 'blur' }],
    deleteStatus: [{ required: true, message: '删除状态 0未删除 1已删除不能为空', trigger: 'change' }],
  },
});

const { form, rules } = toRefs(data);

const selectModel = (item) => {
  activeModel.value = item;
};

const graph = null; // 👈 在外层声明

watch(activeModel, async (newVal) => {
  if (!newVal) return;
  await nextTick();

  /*  const container = document.getElementById('g6-container')
  if (!container) return
  // Get the Data.
  const data = {
    nodes: [
      { id: 'node1', label: '节点1' },
      { id: 'node2', label: '节点2' },
    ],
    edges: [
      { source: 'node1', target: 'node2' },
    ],
  };

// Create the Graph instance.
   graph = new Graph({
    container: container,
    data,
    node: {
      palette: {
        type: 'group',
        field: 'cluster',
      },
    },
    layout: {
      type: 'force',
    },
    behaviors: ['drag-canvas', 'drag-node'],
  });

// Render the Graph.
  graph.render();*/
});

function showContextMenu(event, item) {
  event.preventDefault();
  contextMenuVisible.value = true;
  menuPosition.value = {
    x: event.offsetX,
    y: event.offsetY,
  };
  console.log('event', event);
  activeContextItem.value = item;
}

/**
 * 设置首图
 */
function configFirstModel() {
  /* activeContextItem*/
  ElMessageBox.confirm('是否要设置3D模型编号为"' + activeContextItem.value.name + '"为首图？')
    .then(function () {
      activeContextItem.value.startStatus = 1;
      SetStartStatus(activeContextItem.value).then((res) => {
        if (res.code == 200) {
          ElMessage.success('更新成功');
          getList();
        }
      });
    })
    .catch(() => {});
}

// 点击其他区域关闭菜单
function handleClickOutside() {
  contextMenuVisible.value = false;
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});
onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside);
});

/** 查询业务 holo工厂3D模型列表 */
function getList() {
  loading.value = true;
  listModel(queryParams.value).then((response) => {
    modelList.value = response.data.list;
    total.value = response.data.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    groupCode: null,
    organizeCode: null,
    objCode: null,
    code: null,
    desc: null,
    name: null,
    buildingCode: null,
    floorCode: null,
    regionCode: null,
    factoryCode: null,
    isSystem: null,
    startStatus: null,
    isCommon: null,
    modelType: null,
    suffix: null,
    modelImg: null,
    deleteStatus: null,
    createTime: null,
    updateTime: null,
    createUserCode: null,
    updateUserCode: null,
    remark: null,
  };
  proxy.resetForm('modelRef');
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.page = 1;
  getList();
  activeModel.value = null;
}

/** 重置按钮操作 */
function resetQuery() {
  queryParams.value = {
    page: 1,
    size: 20,
    factoryCode: '',
    regionCode: '',
    buildingCode: '',
    floorCode: '',
    name: '',
    orderNum: '',
    orderByColumn: '',
    isAsc: '',
  };
  regionOptions.value = [];
  buildingOptions.value = [];
  floorOptions.value = [];
  getList();
  activeModel.value = null;
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  // 在当前浏览器中打开新的tab页，使用全屏3D编辑器
  const editorUrl = window.location.origin + '/3d/editor/fullscreen';
  window.open(editorUrl, '_blank');
}

/** 新增模型弹窗操作 */
function handleAddModel() {
  reset();
  // 清空弹窗选项数据
  formRegionOptions.value = [];
  formBuildingOptions.value = [];
  formFloorOptions.value = [];
  open.value = true;
  title.value = '添加业务 holo工厂3D模型';
}

/** 修改按钮操作 */
async function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  try {
    const response = await getModel(_id);
    form.value = response.data;

    // 如果有工厂代码，加载对应的区域、楼栋、楼层选项
    if (form.value.factoryCode) {
      await handleFormFactoryChange();

      if (form.value.regionCode) {
        await handleFormRegionChange();

        if (form.value.buildingCode) {
          handleFormBuildingChange();
        }
      }
    }

    open.value = true;
    title.value = '修改业务 holo工厂3D模型';
  } catch (error) {
    console.error('获取模型数据失败:', error);
    ElMessage.error('获取模型数据失败');
  }
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs['modelRef'].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateModel(form.value).then((response) => {
          ElMessage.success('修改成功');
          open.value = false;
          getList();
        });
      } else {
        addModel(form.value).then((response) => {
          ElMessage.success('新增成功');
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  ElMessageBox.confirm('是否确认删除业务 holo工厂3D模型编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delModel(_ids);
    })
    .then(() => {
      getList();
      ElMessage.success('删除成功');
    })
    .catch(() => {});
}

/** 处理分页回调事件 */
function handlePaginationEvent(pagination) {
  const { pageNum, pageSize } = pagination;
  queryParams.value.page = pageNum;
  queryParams.value.size = pageSize;
  getList();
}

/** 切换视图模式 */
function toggleViewMode() {
  viewMode.value = viewMode.value === 'table' ? 'card' : 'table';
}

// 弹窗专用的工厂切换处理
const handleFormFactoryChange = async () => {
  form.value.regionCode = '';
  form.value.buildingCode = '';
  form.value.floorCode = '';
  formRegionOptions.value = [];
  formBuildingOptions.value = [];
  formFloorOptions.value = [];

  if (!form.value.factoryCode) return;

  // 通过工厂code找到organizeCode
  const selectedFactory = factoryOptions.value.find((item) => item.code === form.value.factoryCode);
  const organizeCode = selectedFactory ? selectedFactory.organizeCode : '';
  if (!organizeCode) return;

  try {
    // 用organizeCode请求区域
    const res = await getRegionList({ organizeCode });
    // 添加工厂区选项
    formRegionOptions.value = [{ code: '0', name: '工厂区' }, ...(res.data.list || [])];
  } catch (error) {
    console.error('获取区域列表失败:', error);
    ElMessage.error('获取区域列表失败');
  }
};

// 弹窗专用的区域切换处理
const handleFormRegionChange = async () => {
  form.value.buildingCode = '';
  form.value.floorCode = '';
  formBuildingOptions.value = [];
  formFloorOptions.value = [];

  if (!form.value.regionCode || form.value.regionCode === '0') return;

  try {
    // 获取区域详情，包含关联的楼栋和楼层信息
    const res = await getRegion(form.value.regionCode);
    if (res.data) {
      // 获取关联的楼栋信息
      const param = {
        organizeCode: res.data.organizeCode,
        codes: res.data.buildingCodes,
        getFloor: true,
      };
      const buildingRes = await findBuildingListWithFloor(param);
      if (buildingRes.data) {
        // 保存区域关联的楼层信息，供楼栋选择时使用
        const regionFloorCodes = res.data.buildingFloorCodes || [];
        formBuildingOptions.value = buildingRes.data.map((building) => ({
          ...building,
          regionFloorCodes: regionFloorCodes,
        }));
      }
    }
  } catch (error) {
    console.error('获取区域关联楼栋楼层失败:', error);
    ElMessage.error('获取区域关联楼栋楼层失败');
  }
};

// 弹窗专用的楼栋切换处理
const handleFormBuildingChange = () => {
  form.value.floorCode = '';
  formFloorOptions.value = [];

  if (!form.value.buildingCode) return;

  const selectedBuilding = formBuildingOptions.value.find((item) => item.code === form.value.buildingCode);
  if (selectedBuilding) {
    // 获取楼栋的所有楼层
    const allFloors = selectedBuilding.children || [];
    // 获取区域关联的楼层代码
    const regionFloorCodes = selectedBuilding.regionFloorCodes || [];

    // 过滤出区域关联的楼层
    formFloorOptions.value = allFloors.filter((floor) => {
      return regionFloorCodes.includes(floor.code);
    });

    if (formFloorOptions.value.length === 0) {
      ElMessage.warning('该楼栋下没有与区域关联的楼层');
    }
  }
};

// 获取工厂列表
const fetchFactoryOptions = async () => {
  const res = await getUserFactory();
  factoryOptions.value = res.data || [];
};

// 获取区域列表
const fetchRegionOptions = async () => {
  if (!queryParams.value.factoryCode) {
    regionOptions.value = [];
    buildingOptions.value = [];
    floorOptions.value = [];
    return;
  }
  // 通过工厂code找到organizeCode
  const selectedFactory = factoryOptions.value.find((item) => item.code === queryParams.value.factoryCode);
  const organizeCode = selectedFactory ? selectedFactory.organizeCode : '';
  if (!organizeCode) {
    regionOptions.value = [];
    buildingOptions.value = [];
    floorOptions.value = [];
    return;
  }
  // 用organizeCode请求区域
  const res = await getRegionList({ organizeCode });
  // 添加工厂区选项
  regionOptions.value = [{ code: '0', name: '工厂区' }, ...(res.data.list || [])];

  // 加载楼栋列表，只在非工作区的情况下加载
  if (queryParams.value.regionCode !== '0') {
    const buildingRes = await findBuildingListWithFloor({ organizeCode });
    buildingOptions.value = buildingRes.data || [];
  }
};

// 工厂切换时加载区域
const handleFactoryChange = () => {
  queryParams.value.regionCode = '';
  queryParams.value.buildingCode = '';
  queryParams.value.floorCode = '';
  fetchRegionOptions();
};

// 区域切换时加载关联的楼栋和楼层
const handleRegionChange = async () => {
  // 保存当前的楼栋和楼层代码
  const currentBuildingCode = queryParams.value.buildingCode;
  const currentFloorCode = queryParams.value.floorCode;

  // 只在手动切换区域时清空
  if (!queryParams.value.regionCode) {
    queryParams.value.buildingCode = '';
    queryParams.value.floorCode = '';
  }

  buildingOptions.value = [];
  floorOptions.value = [];

  if (!queryParams.value.regionCode) {
    return;
  }

  // 如果选择的是工作区（code为0），则清空楼栋和楼层
  if (queryParams.value.regionCode === '0') {
    queryParams.value.buildingCode = '';
    queryParams.value.floorCode = '';
    return;
  }

  try {
    // 获取区域详情，包含关联的楼栋和楼层信息
    const res = await getRegion(queryParams.value.regionCode);
    if (res.data) {
      // 获取关联的楼栋信息
      const param = {
        organizeCode: res.data.organizeCode,
        codes: res.data.buildingCodes,
        getFloor: true,
      };
      const buildingRes = await findBuildingListWithFloor(param);
      if (buildingRes.data) {
        // 保存区域关联的楼层信息，供楼栋选择时使用
        const regionFloorCodes = res.data.buildingFloorCodes || [];
        buildingOptions.value = buildingRes.data.map((building) => ({
          ...building,
          regionFloorCodes: regionFloorCodes, // 将区域关联的楼层代码保存到楼栋对象中
        }));

        // 如果是恢复查询参数，保持原有的楼栋和楼层代码
        if (currentBuildingCode) {
          queryParams.value.buildingCode = currentBuildingCode;
          handleBuildingChange(currentFloorCode);
        }
      }
    }
  } catch (error) {
    console.error('获取区域关联楼栋楼层失败:', error);
    ElMessage.error('获取区域关联楼栋楼层失败');
  }
};

// 楼栋切换时加载楼层
const handleBuildingChange = (preserveFloorCode) => {
  // 保存当前的楼层代码
  const currentFloorCode = preserveFloorCode || queryParams.value.floorCode;

  // 只在手动切换楼栋时清空
  if (!queryParams.value.buildingCode) {
    queryParams.value.floorCode = '';
  }

  floorOptions.value = [];

  if (!queryParams.value.buildingCode) {
    return;
  }

  const selectedBuilding = buildingOptions.value.find((item) => item.code === queryParams.value.buildingCode);
  if (selectedBuilding) {
    // 获取楼栋的所有楼层
    const allFloors = selectedBuilding.children || [];
    // 获取区域关联的楼层代码
    const regionFloorCodes = selectedBuilding.regionFloorCodes || [];

    // 过滤出区域关联的楼层
    floorOptions.value = allFloors.filter((floor) => {
      // 确保楼层代码匹配
      const isMatched = regionFloorCodes.includes(floor.code);
      if (isMatched && currentFloorCode === floor.code) {
        // 如果找到匹配的楼层，保持选中状态
        nextTick(() => {
          queryParams.value.floorCode = floor.code;
        });
      }
      return isMatched;
    });

    if (floorOptions.value.length === 0) {
      ElMessage.warning('该楼栋下没有与区域关联的楼层');
    }
  }
};

function saveQueryParams() {
  const cache = JSON.parse(sessionStorage.getItem(QUERY_CACHE_KEY) || '{}');
  cache[route.path] = { ...queryParams.value };
  sessionStorage.setItem(QUERY_CACHE_KEY, JSON.stringify(cache));
}

function restoreQueryParams() {
  const cache = JSON.parse(sessionStorage.getItem(QUERY_CACHE_KEY) || '{}');
  if (cache[route.path]) {
    Object.assign(queryParams.value, cache[route.path]);
  }
}

/**
 * 初始化加载
 */
onMounted(async () => {
  await fetchFactoryOptions(); // 先加载工厂列表
  restoreQueryParams(); // 恢复查询参数
  if (queryParams.value.factoryCode) {
    await fetchRegionOptions(); // 加载区域列表
  }
  if (queryParams.value.regionCode) {
    await handleRegionChange(); // 加载楼栋和楼层
  }
  console.log('queryParams.value', queryParams.value);
  getList(); // 最后加载数据列表
});
watch(() => route.path, restoreQueryParams);
onBeforeUnmount(saveQueryParams);
watch(queryParams, saveQueryParams, { deep: true });
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.search-bar {
  margin-bottom: 20px;
}

/* 卡片视图样式 */
.card-container {
  margin-top: 20px;
  /*overflow-y: auto;*/
  overflow-x: hidden;
}

.scroll-container {
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ */
}

.scroll-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Edge */
}

.card-item {
  margin-top: 10px;
  width: 80%;
  box-sizing: border-box;
  /* margin-bottom: 20px;*/
}

.model-card {
  margin-bottom: 10px;
  transition: all 0.3s ease;
  padding: 0 0 10px 0 !important;
  box-sizing: border-box;
  border: 1px solid rgb(179, 172, 172);
  border-radius: 3px;
}

.model-card:hover {
  transform: translateY(-2px);

  /* box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);*/
}

.card-wrapper {
  position: relative;
  width: 100%;
}

.card-image-container {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  overflow: hidden;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card-image-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.card-content {
  padding-top: 10px;
  padding-left: 10px;
  width: 100%;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  box-sizing: border-box;
  color: #303133;
}

.card-desc {
  color: #666;
  font-size: 14px;
  margin-bottom: 12px;
  height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.card-info {
  margin-bottom: 12px;
}

.card-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
}

.info-label {
  color: #909399;
  font-weight: 500;
}

.info-value {
  color: #606266;
  font-weight: 600;
}

.card-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.el-card {
  width: 100%;
}

:deep(.el-card__body) {
  width: 100%;
  padding: 0 !important;
}

.card-image-container {
  height: 100% !important;
  border-radius: 1px !important;
}

/* 确保所有内容都在容器内 */
.el-card,
.el-card__body,
.card-wrapper,
.card-content,
.card-title {
  box-sizing: border-box;
  overflow: hidden;
}

.context-menu {
  position: absolute;
  z-index: 999;
  background-color: #fff;
  border: 1px solid #ccc;
  list-style: none;
  padding: 0;
  margin: 0;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transform: translate(-50%);
}

.is-active{
  border: 3px solid #00aaff;
}
</style>
