<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">

      <el-form-item label="专案名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入专案名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工厂" prop="factoryCode">
        <el-select
          v-model="queryParams.factoryCode"
          placeholder="请选择工厂"
          clearable
          style="width: 200px"
          @change="handleQuery"
        >
          <el-option
            v-for="item in factoryOptions"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="工厂编号" prop="factNo">
        <el-input
          v-model="queryParams.factNo"
          placeholder="请输入工厂编号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['HoloProject:holoProject:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['HoloProject:holoProject:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['HoloProject:holoProject:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['HoloProject:holoProject:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="holoProjectList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="唯一ID" align="center" prop="id" />
      <el-table-column label="名称" align="center" prop="name" />
      <el-table-column label="描述" align="center" prop="desc" />
      <el-table-column label="工厂" align="center" prop="factoryName" />
      <el-table-column label="工厂编号" align="center" prop="factNo" />
      <el-table-column label="项目类型" align="center" prop="projectType" />
      <el-table-column label="项目主题色" align="center" prop="projectColor">
        <template #default="scope">
          <el-tag :type="scope.row.projectColor === 0 ? 'info' : 'warning'">
            {{ scope.row.projectColor === 0 ? '浅色' : scope.row.projectColor === 1 ? '深色' : '未知' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="项目支持语言" align="center" prop="projectLanguage">
        <template #default="scope">
          <div v-if="scope.row.projectLanguage">
            <el-tag
              v-for="lang in scope.row.projectLanguage.split(',')"
              :key="lang"
              size="small"
              style="margin: 2px"
            >
              {{ getLanguageName(lang) }}
            </el-tag>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="是否支持AI问答" align="center" prop="isAi">
        <template #default="scope">
          <el-switch
            v-model="scope.row.isAi"
            :active-value="1"
            :inactive-value="0"
            disabled
            size="small"
          />
        </template>
      </el-table-column>
      <el-table-column label="项目链接" align="center" prop="projectUrl">
        <template #default="scope">
          <el-link
            v-if="scope.row.projectUrl"
            :href="scope.row.projectUrl"
            target="_blank"
            type="primary"
          >
            查看项目
          </el-link>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="项目状态" align="center" prop="projectStatus">
        <template #default="scope">
          <el-tag
            :type="getStatusType(scope.row.projectStatus)"
            effect="dark"
          >
            {{ getStatusText(scope.row.projectStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['HoloProject:holoProject:edit']">修改</el-button>
          <el-button
            link
            :type="getStatusButtonType(scope.row.projectStatus)"
            @click="handleStatusChange(scope.row)"
            v-hasPermi="['HoloProject:holoProject:edit']"
          >
            {{ getStatusButtonText(scope.row.projectStatus) }}
          </el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['HoloProject:holoProject:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.page"
      v-model:limit="queryParams.size"
      @pagination="getList"
    />

    <!-- 添加或修改业务 holo项目发布管理对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="holoProjectRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="专案名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入专案名称" />
        </el-form-item>
        <el-form-item label="专案介绍" prop="desc">
          <el-input v-model="form.desc" placeholder="请输入专案介绍" />
        </el-form-item>
        <el-form-item label="工厂" prop="factoryCode">
          <el-select
            v-model="form.factoryCode"
            placeholder="请选择工厂"
            clearable
            style="width: 100%"
            :disabled="isEditMode"
          >
            <el-option
              v-for="item in factoryOptions"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="工厂编号" prop="factNo">
          <el-input v-model="form.factNo" placeholder="请输入工厂编号" />
        </el-form-item>
        <el-form-item label="项目主题" prop="projectType">
          <el-select
            v-model="form.projectType"
            placeholder="请选择项目主题"
            clearable
            style="width: 100%"
            :disabled="isEditMode"
          >
            <el-option label="全息" :value="0" />
            <el-option label="3D" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="项目主题色" prop="projectColor">
          <el-radio-group v-model="form.projectColor">
            <el-radio :value="0">浅色</el-radio>
            <el-radio :value="1">深色</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="项目支持语言" prop="projectLanguage">
          <el-checkbox-group v-model="selectedLanguages" @change="handleLanguageChange">
            <el-checkbox value="zh-CN">中文简体</el-checkbox>
            <el-checkbox value="zh-TW">中文繁体</el-checkbox>
            <el-checkbox value="en-US">英语</el-checkbox>
            <el-checkbox value="ja-JP">日语</el-checkbox>
            <el-checkbox value="ko-KR">韩语</el-checkbox>
            <el-checkbox value="fr-FR">法语</el-checkbox>
            <el-checkbox value="de-DE">德语</el-checkbox>
            <el-checkbox value="es-ES">西班牙语</el-checkbox>
            <el-checkbox value="ru-RU">俄语</el-checkbox>
            <el-checkbox value="ar-SA">阿拉伯语</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="是否支持AI问答" prop="isAi">
          <el-switch
            v-model="form.isAi"
            :active-value="1"
            :inactive-value="0"
            active-text="是"
            inactive-text="否"
            style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="HoloProject">
import { getCurrentInstance, onMounted, computed } from 'vue';
import { listHoloProject, getHoloProject, delHoloProject, addHoloProject, updateHoloProject } from "@/api/holo/holoProject";
import { getUserFactory } from "@/api/holo/factory";
import { ElMessage, ElMessageBox } from 'element-plus';
const { proxy } = getCurrentInstance();

const holoProjectList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const factoryOptions = ref([]);
const selectedLanguages = ref([]);

// 计算属性：判断是否为编辑模式
const isEditMode = computed(() => {
  return form.value.id != null;
});

const data = reactive({
  form: {},
  queryParams: {
    page: 1,
    size: 10,
    groupCode: null,
    organizeCode: null,
    code: null,
    desc: null,
    name: null,
    factoryCode: null,
    factNo: null,
    projectType: null,
    projectColor: null,
    projectLanguage: null,
    isAi: null,
    projectUrl: null,
    projectStatus: null,
    deleteStatus: null,
    createUserCode: null,
    updateUserCode: null,
  },
  rules: {
    groupCode: [
      { required: true, message: "$comment不能为空", trigger: "blur" }
    ],
    organizeCode: [
      { required: true, message: "$comment不能为空", trigger: "blur" }
    ],
    code: [
      { required: true, message: "$comment不能为空", trigger: "blur" }
    ],
    name: [
      { required: true, message: "$comment不能为空", trigger: "blur" }
    ],
    projectType: [
      { required: true, message: "项目类型不能为空", trigger: "change" }
    ],
    projectColor: [
      { required: true, message: "项目主题色  (0:浅色 1：深色)不能为空", trigger: "blur" }
    ],
    projectUrl: [
      { required: true, message: "项目链接不能为空", trigger: "blur" }
    ],
    projectStatus: [
      { required: true, message: "当前专案状态：0，未发布 1，发布中  2，已停用不能为空", trigger: "change" }
    ],
    deleteStatus: [
      { required: true, message: "删除状态 0未删除 1已删除不能为空", trigger: "change" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询业务 holo项目发布管理列表 */
function getList() {
  loading.value = true;
  listHoloProject(queryParams.value).then(response => {
    holoProjectList.value = response.data.list;
    total.value = response.data.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    groupCode: null,
    organizeCode: null,
    code: null,
    desc: null,
    name: null,
    factoryCode: null,
    factNo: null,
    projectType: null,
    projectColor: null,
    projectLanguage: null,
    isAi: null,
    projectUrl: null,
    projectStatus: null,
    deleteStatus: null,
    createTime: null,
    updateTime: null,
    createUserCode: null,
    updateUserCode: null,
    remark: null
  };
  selectedLanguages.value = [];
  proxy.resetForm("holoProjectRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.page = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加业务 holo项目发布管理";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getHoloProject(_id).then(response => {
    form.value = response.data;
    // 处理语言数据：将逗号分隔的字符串转换为数组
    if (form.value.projectLanguage) {
      selectedLanguages.value = form.value.projectLanguage.split(',');
    } else {
      selectedLanguages.value = [];
    }
    open.value = true;
    title.value = "修改业务 holo项目发布管理";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["holoProjectRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateHoloProject(form.value).then(response => {
          ElMessage.success("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addHoloProject(form.value).then(response => {
          ElMessage.success("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  ElMessageBox.confirm('是否确认删除业务 holo项目发布管理编号为"' + _ids + '"的数据项？').then(function() {
    return delHoloProject(_ids);
  }).then(() => {
    getList();
    ElMessage.success("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
/*
function handleExport() {
  proxy.download('HoloProject/holoProject/export', {
    ...queryParams.value
  }, `holoProject_${new Date().getTime()}.xlsx`)
}
*/

// 获取工厂列表
function getFactoryOptions() {
  getUserFactory().then(response => {
    factoryOptions.value = response.data || [];
  }).catch(error => {
    console.error('获取工厂列表失败:', error);
  });
}

// 处理语言选择变化
function handleLanguageChange(value) {
  // 将选中的语言数组转换为逗号分隔的字符串
  form.value.projectLanguage = value.join(',');
}

// 获取语言显示名称
function getLanguageName(langCode) {
  const languageMap = {
    'zh-CN': '中文简体',
    'zh-TW': '中文繁体',
    'en-US': '英语',
    'ja-JP': '日语',
    'ko-KR': '韩语',
    'fr-FR': '法语',
    'de-DE': '德语',
    'es-ES': '西班牙语',
    'ru-RU': '俄语',
    'ar-SA': '阿拉伯语'
  };
  return languageMap[langCode] || langCode;
}

// 获取项目状态文本
function getStatusText(status) {
  const statusMap = {
    0: '未发布',
    1: '发布中',
    2: '已停用'
  };
  return statusMap[status] || '未知';
}

// 获取项目状态标签类型
function getStatusType(status) {
  const typeMap = {
    0: 'info',     // 未发布 - 灰色
    1: 'success',  // 发布中 - 绿色
    2: 'danger'    // 已停用 - 红色
  };
  return typeMap[status] || 'info';
}

// 获取状态按钮文本
function getStatusButtonText(status) {
  const textMap = {
    0: '发布',     // 未发布 -> 发布
    1: '下架',     // 发布中 -> 下架
    2: '上架'      // 已停用 -> 上架
  };
  return textMap[status] || '发布';
}

// 获取状态按钮类型
function getStatusButtonType(status) {
  const typeMap = {
    0: 'success',  // 未发布 -> 发布按钮(绿色)
    1: 'warning',  // 发布中 -> 下架按钮(橙色)
    2: 'success'   // 已停用 -> 上架按钮(绿色)
  };
  return typeMap[status] || 'success';
}

// 处理状态变更
function handleStatusChange(row) {
  const statusMap = {
    0: { newStatus: 1, action: '发布' },     // 未发布 -> 发布中
    1: { newStatus: 2, action: '下架' },     // 发布中 -> 已停用
    2: { newStatus: 1, action: '上架' }      // 已停用 -> 发布中
  };

  const config = statusMap[row.projectStatus];
  if (!config) {
    ElMessage.warning('无效的项目状态');
    return;
  }

  ElMessageBox.confirm(`确认要${config.action}项目"${row.name}"吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 创建更新对象
    const updateData = {
      ...row,
      projectStatus: config.newStatus
    };

    updateHoloProject(updateData).then(() => {
      ElMessage.success(`${config.action}成功`);
      getList(); // 刷新列表
    }).catch(error => {
      console.error(`${config.action}失败:`, error);
      ElMessage.error(`${config.action}失败`);
    });
  }).catch(() => {
    // 用户取消操作
  });
}

// 页面初始化
onMounted(() => {
  getFactoryOptions();
});

getList();
</script>
