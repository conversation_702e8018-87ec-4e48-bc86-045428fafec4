<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>亮点工厂筛选测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .test-item {
            margin: 10px 0;
            padding: 8px;
            background: #f9f9f9;
            border-radius: 3px;
            display: flex;
            align-items: center;
        }
        .test-name {
            flex: 1;
            font-weight: bold;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 200px;
        }
        .test-factory {
            margin-left: 10px;
            color: #666;
            font-size: 12px;
        }
        .test-type {
            margin-left: 10px;
            color: #999;
            font-size: 11px;
        }
        .filter-section {
            margin-bottom: 20px;
        }
        .filter-section select {
            margin-right: 10px;
            padding: 5px;
        }
        .status {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>亮点工厂筛选功能测试</h1>
        
        <div class="test-section">
            <div class="test-title">功能说明</div>
            <p>1. <strong>亮点名称过长的鼠标悬浮显示名称</strong> - 将鼠标悬停在亮点名称上查看完整名称</p>
            <p>2. <strong>工厂筛选功能</strong> - 使用下方的工厂选择器筛选不同工厂的亮点</p>
        </div>

        <div class="test-section">
            <div class="test-title">筛选控件</div>
            <div class="filter-section">
                <select id="factorySelect">
                    <option value="">全部工厂</option>
                    <option value="FACTORY_001">上海工厂</option>
                    <option value="FACTORY_002">北京工厂</option>
                    <option value="FACTORY_003">深圳工厂</option>
                </select>
                
                <select id="typeSelect">
                    <option value="">全部类型</option>
                    <option value="1">文字</option>
                    <option value="3">图片</option>
                    <option value="4">视频</option>
                    <option value="5">看板</option>
                </select>
            </div>
            <div class="status" id="statusText">显示 0 / 0 个亮点</div>
        </div>

        <div class="test-section">
            <div class="test-title">亮点列表</div>
            <div id="spotlightList"></div>
        </div>
    </div>

    <script>
        // 模拟亮点数据
        const mockSpotlightData = [
            {
                id: 1,
                nameI18n: { zhContent: "这是一个非常长的亮点名称用于测试鼠标悬浮显示功能" },
                factoryCode: "FACTORY_001",
                dataType: 1,
                iconColor: "#ff6b6b"
            },
            {
                id: 2,
                nameI18n: { zhContent: "短名称" },
                factoryCode: "FACTORY_002", 
                dataType: 3,
                iconColor: "#4ecdc4"
            },
            {
                id: 3,
                nameI18n: { zhContent: "另一个超级超级超级长的亮点名称测试文本溢出效果" },
                factoryCode: "FACTORY_001",
                dataType: 4,
                iconColor: "#45b7d1"
            },
            {
                id: 4,
                nameI18n: { zhContent: "普通亮点" },
                factoryCode: "FACTORY_003",
                dataType: 5,
                iconColor: "#96ceb4"
            },
            {
                id: 5,
                nameI18n: { zhContent: "测试工厂筛选功能的亮点名称比较长一些" },
                factoryCode: "FACTORY_002",
                dataType: 1,
                iconColor: "#feca57"
            }
        ];

        // 工厂数据
        const factoryData = [
            { code: "FACTORY_001", name: "上海工厂" },
            { code: "FACTORY_002", name: "北京工厂" },
            { code: "FACTORY_003", name: "深圳工厂" }
        ];

        // 类型映射
        const typeMap = {
            1: '文字',
            2: '语音',
            3: '图片',
            4: '视频',
            5: '看板',
            6: '富文本'
        };

        // 获取工厂名称
        function getFactoryName(factoryCode) {
            const factory = factoryData.find(f => f.code === factoryCode);
            return factory ? factory.name : factoryCode;
        }

        // 渲染亮点列表
        function renderSpotlightList(data = mockSpotlightData) {
            const listContainer = document.getElementById('spotlightList');
            listContainer.innerHTML = '';

            if (data.length === 0) {
                listContainer.innerHTML = '<div style="text-align: center; color: #999; padding: 20px;">暂无亮点数据</div>';
                return;
            }

            data.forEach(spotlight => {
                const item = document.createElement('div');
                item.className = 'test-item';
                
                const fullName = spotlight.nameI18n?.zhContent || spotlight.name || '未命名';
                
                item.innerHTML = `
                    <div class="test-name" title="${fullName}">${fullName}</div>
                    <div class="test-factory">${getFactoryName(spotlight.factoryCode)}</div>
                    <div class="test-type">${typeMap[spotlight.dataType] || '未知'}</div>
                `;
                
                listContainer.appendChild(item);
            });
        }

        // 筛选功能
        function filterSpotlights() {
            const selectedFactory = document.getElementById('factorySelect').value;
            const selectedType = document.getElementById('typeSelect').value;

            const filteredData = mockSpotlightData.filter(spotlight => {
                const factoryMatch = !selectedFactory || spotlight.factoryCode === selectedFactory;
                const typeMatch = !selectedType || spotlight.dataType == selectedType;
                return factoryMatch && typeMatch;
            });

            renderSpotlightList(filteredData);
            document.getElementById('statusText').textContent = 
                `显示 ${filteredData.length} / ${mockSpotlightData.length} 个亮点`;
        }

        // 绑定事件
        document.getElementById('factorySelect').addEventListener('change', filterSpotlights);
        document.getElementById('typeSelect').addEventListener('change', filterSpotlights);

        // 初始化
        filterSpotlights();
    </script>
</body>
</html>
