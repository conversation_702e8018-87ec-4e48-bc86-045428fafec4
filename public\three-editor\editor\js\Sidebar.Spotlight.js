import * as THREE from 'three';
import { UIPanel, UIRow, UIText, UIButton, UIBreak, UIInput, UISelect } from './libs/ui.js';
import { AddObjectCommand } from './commands/AddObjectCommand.js';

// 图标路径映射
const iconPaths = {
	'button': 'M230.4 307.712c13.824 0 25.088-11.264 25.088-25.088 0-100.352 81.92-182.272 182.272-182.272s182.272 81.408 182.272 182.272c0 13.824 11.264 25.088 25.088 25.088s25.088-11.264 24.576-25.088c0-127.488-103.936-231.936-231.936-231.936S205.824 154.624 205.824 282.624c-0.512 14.336 10.752 25.088 24.576 25.088z m564.736 234.496c-11.264 0-21.504 2.048-31.232 6.144 0-44.544-40.448-81.92-88.064-81.92-14.848 0-28.16 3.584-39.936 10.24-13.824-28.16-44.544-48.128-78.848-48.128-12.288 0-24.576 2.56-35.328 7.68V284.16c0-45.568-37.888-81.92-84.48-81.92s-84.48 36.864-84.48 81.92v348.672l-69.12-112.64c-18.432-28.16-58.368-36.864-91.136-19.968-26.624 14.336-46.592 47.104-30.208 88.064 3.072 8.192 76.8 205.312 171.52 311.296 0 0 28.16 24.576 43.008 58.88 4.096 9.728 13.312 15.36 22.528 15.36 3.072 0 6.656-0.512 9.728-2.048 12.288-5.12 18.432-19.968 12.8-32.256-19.456-44.544-53.76-74.752-53.76-74.752C281.6 768 209.408 573.44 208.384 570.88c-5.12-12.8-2.56-20.992 7.168-26.112 9.216-4.608 21.504-4.608 26.112 2.56l113.152 184.32c4.096 8.704 12.8 14.336 22.528 14.336 13.824 0 25.088-10.752 25.088-25.088V284.16c0-17.92 15.36-32.256 34.816-32.256s34.816 14.336 34.816 32.256v284.16c0 13.824 10.24 25.088 24.576 25.088 13.824 0 25.088-11.264 25.088-25.088v-57.344c0-17.92 15.36-32.768 34.816-32.768 19.968 0 37.376 15.36 37.376 32.768v95.232c0 7.168 3.072 13.312 7.68 17.92 4.608 4.608 10.752 7.168 17.92 7.168 13.824 0 24.576-11.264 24.576-25.088V547.84c0-18.432 13.824-32.256 32.256-32.256 20.48 0 38.912 15.36 38.912 32.256v95.232c0 13.824 11.264 25.088 25.088 25.088s24.576-11.264 25.088-25.088v-18.944c0-18.944 12.8-32.256 30.72-32.256 18.432 0 22.528 18.944 22.528 31.744 0 1.024-11.776 99.84-50.688 173.056-30.72 58.368-45.056 112.128-51.2 146.944-2.56 13.312 6.656 26.112 19.968 28.672 1.536 0 3.072 0.512 4.608 0.512 11.776 0 22.016-8.192 24.064-20.48 5.632-31.232 18.432-79.36 46.08-132.608 43.52-81.92 55.808-186.88 56.32-193.536-0.512-50.688-29.696-83.968-72.704-83.968z',
	'monitor': 'M64 64V640H896V64H64zM0 0h960v704H0V0z M192 896H768v64H192zM448 640H512v256h-64z',
	'server': 'M890 120H134a70 70 0 0 0-70 70v500a70 70 0 0 0 70 70h756a70 70 0 0 0 70-70V190a70 70 0 0 0-70-70z m-10 520a40 40 0 0 1-40 40H712V448a40 40 0 0 0-80 0v232h-80V368a40 40 0 0 0-80 0v312h-80V512a40 40 0 0 0-80 0v168H184a40 40 0 0 1-40-40V240a40 40 0 0 1 40-40h656a40 40 0 0 1 40 40zM696 824H328a40 40 0 0 0 0 80h368a40 40 0 0 0 0-80z'
};

// 获取图标路径
function getIconPath( iconCode ) {
	return iconPaths[iconCode] || iconPaths['button']; // 默认使用button图标
}

function SidebarSpotlight( editor ) {

	console.log( '🌟 创建独立亮点侧边栏...' );

	const container = new UIPanel();
	container.setId( 'spotlight' );

	// 标题
	container.add( new UIText( '亮点管理' ).setTextTransform( 'uppercase' ) );
	container.add( new UIBreak(), new UIBreak() );

	// 状态显示
	const statusText = new UIText( '正在加载亮点数据...' );
	container.add( statusText );
	container.add( new UIBreak() );

	// 搜索区域
	const searchRow = new UIRow();
	const searchLabel = new UIText( '搜索' ).setWidth( '60px' );
	const searchInput = new UIInput( '' ).setWidth( '120px' );
	searchInput.dom.placeholder = '输入亮点名称';
	searchRow.add( searchLabel );
	searchRow.add( searchInput );
	container.add( searchRow );
	container.add( new UIBreak() );

	// 工厂筛选
	const factoryRow = new UIRow();
	const factoryLabel = new UIText( '工厂' ).setWidth( '60px' );
	const factorySelect = new UISelect().setWidth( '120px' );
	factorySelect.setOptions( {
		'': '全部工厂'
	} );
	factoryRow.add( factoryLabel );
	factoryRow.add( factorySelect );
	container.add( factoryRow );
	container.add( new UIBreak() );

	// 类型筛选
	const typeRow = new UIRow();
	const typeLabel = new UIText( '类型' ).setWidth( '60px' );
	const typeSelect = new UISelect().setWidth( '120px' );
	typeSelect.setOptions( {
		'': '全部类型',
		'1': '文字',
		'2': '语音',
		'3': '图片',
		'4': '视频',
		'5': '看板',
		'6': '富文本',
		'7': '标识牌',
		'8': '监控',
		'9': '联网设备'
	} );
	typeRow.add( typeLabel );
	typeRow.add( typeSelect );
	container.add( typeRow );
	container.add( new UIBreak() );

	// 搜索按钮
	const buttonRow = new UIRow();
	const searchButton = new UIButton( '搜索' ).setWidth( '60px' );
	const resetButton = new UIButton( '重置' ).setWidth( '60px' );
	buttonRow.add( searchButton );
	buttonRow.add( resetButton );
	container.add( buttonRow );
	container.add( new UIBreak() );

	// 亮点列表容器
	const listContainer = new UIPanel();
	listContainer.setClass( 'spotlight-list' );
	listContainer.dom.style.maxHeight = '400px';
	listContainer.dom.style.overflow = 'auto';
	listContainer.dom.style.border = '1px solid #ddd';
	listContainer.dom.style.padding = '5px';
	container.add( listContainer );

	// 存储亮点数据
	let spotlightData = [];
	let filteredData = [];
	let factoryData = [];

	// 获取类型标签
	function getTypeLabel( dataType ) {
		const typeMap = {
			1: '文字',
			2: '语音',
			3: '图片', 
			4: '视频',
			5: '看板',
			6: '富文本',
			7: '标识牌',
			8: '监控',
			9: '联网设备'
		};
		return typeMap[ dataType ] || '未知';
	}

	/**
	 * 创建亮点图标纹理
	 * 在Canvas上绘制圆角矩形背景和SVG图标
	 */
	function createSpotlightTexture( spotlight ) {
		const canvas = document.createElement( 'canvas' );
		const size = 256; // 增大canvas尺寸，提高纹理质量
		canvas.width = size;
		canvas.height = size;
		const ctx = canvas.getContext( '2d' );

		// 启用抗锯齿，提高绘制质量
		ctx.imageSmoothingEnabled = true;
		ctx.imageSmoothingQuality = 'high';

		// 绘制圆角矩形背景
		const centerX = size / 2;
		const centerY = size / 2;
		const rectWidth = size - 2; // 进一步减少边距，让矩形几乎占满canvas
		const rectHeight = size - 2;
		const rectX = (size - rectWidth) / 2;
		const rectY = (size - rectHeight) / 2;
		const cornerRadius = 8; // 进一步减小圆角半径，让矩形更方正

		// 创建圆角矩形路径
		ctx.beginPath();
		ctx.moveTo( rectX + cornerRadius, rectY );
		ctx.lineTo( rectX + rectWidth - cornerRadius, rectY );
		ctx.quadraticCurveTo( rectX + rectWidth, rectY, rectX + rectWidth, rectY + cornerRadius );
		ctx.lineTo( rectX + rectWidth, rectY + rectHeight - cornerRadius );
		ctx.quadraticCurveTo( rectX + rectWidth, rectY + rectHeight, rectX + rectWidth - cornerRadius, rectY + rectHeight );
		ctx.lineTo( rectX + cornerRadius, rectY + rectHeight );
		ctx.quadraticCurveTo( rectX, rectY + rectHeight, rectX, rectY + rectHeight - cornerRadius );
		ctx.lineTo( rectX, rectY + cornerRadius );
		ctx.quadraticCurveTo( rectX, rectY, rectX + cornerRadius, rectY );
		ctx.closePath();

		// 填充背景色
		ctx.fillStyle = spotlight.iconColor || '#b2c2c2';
		ctx.fill();

		// 添加边框
		ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
		ctx.lineWidth = 2;
		ctx.stroke();

		// 绘制图标
		if ( spotlight.iconCode ) {
			const symbol = document.querySelector( `#icon-${spotlight.iconCode}` );
			if ( symbol ) {
				console.log( `🎨 Sidebar找到SVG符号: ${spotlight.iconCode}` );

				// 尝试使用Path2D绘制SVG路径
				try {
					const pathElements = symbol.querySelectorAll( 'path' );
					if ( pathElements.length > 0 ) {
						ctx.fillStyle = 'white';
						ctx.save();

						// 采用ResourcePanel的方法 - 基于1024viewBox计算缩放
						ctx.setTransform(1, 0, 0, 1, 0, 0); // 重置变换矩阵

						// 设置裁剪区域为矩形
						ctx.beginPath();
						ctx.rect(rectX, rectY, rectWidth, rectHeight);
						ctx.clip();

						// 计算缩放比例，让图标填满矩形的90%
						const scale = (rectWidth * 0.9) / 24; // 假设SVG实际有效尺寸为24x24，大幅增加缩放
						ctx.translate(rectX + rectWidth/2, rectY + rectHeight/2); // 移动到矩形中心
						ctx.scale(scale, scale);
						ctx.translate(-12, -12); // 基于24x24的中心点偏移

						pathElements.forEach( pathElement => {
							const pathData = pathElement.getAttribute( 'd' );
							if ( pathData ) {
								const path2D = new Path2D( pathData );
								ctx.fill( path2D );
							}
						} );

						ctx.restore();
						console.log( `✅ Sidebar使用Path2D绘制图标: ${spotlight.iconCode}` );
					} else {
						throw new Error( '没有找到path元素' );
					}
				} catch ( error ) {
					console.warn( `⚠️ Sidebar Path2D绘制失败: ${error.message}，使用文字备用` );
					// 备用方案：绘制文字 - 让文字完全占满矩形
					ctx.fillStyle = 'white';
					const fontSize = Math.floor(rectWidth * 0.8); // 进一步增大字体比例
					ctx.font = `bold ${fontSize}px Arial`;
					ctx.textAlign = 'center';
					ctx.textBaseline = 'middle';
					const text = spotlight.iconCode.charAt(0).toUpperCase();
					ctx.fillText( text, centerX, centerY );
				}
			} else {
				console.warn( `⚠️ Sidebar未找到SVG符号: ${spotlight.iconCode}，使用文字备用` );
				// 绘制文字 - 让文字完全占满矩形
				ctx.fillStyle = 'white';
				const fontSize = Math.floor(rectWidth * 0.8); // 进一步增大字体比例
				ctx.font = `bold ${fontSize}px Arial`;
				ctx.textAlign = 'center';
				ctx.textBaseline = 'middle';
				const text = spotlight.iconCode.charAt(0).toUpperCase();
				ctx.fillText( text, centerX, centerY );
			}
		} else {
			// 默认符号 - 大幅增大字体
			ctx.fillStyle = 'white';
			const fontSize = Math.floor(rectWidth * 0.6); // 动态计算字体大小
			ctx.font = `bold ${fontSize}px Arial`;
			ctx.textAlign = 'center';
			ctx.textBaseline = 'middle';
			ctx.fillText( '●', centerX, centerY );
		}

		return canvas;
	}

	// 添加亮点到场景
	function addSpotlightToScene( spotlight ) {
		try {
			console.log( '🎯 Sidebar开始创建亮点精灵:', spotlight );

			// 创建Canvas纹理
			const canvas = createSpotlightTexture( spotlight );
			const texture = new THREE.CanvasTexture( canvas );
			texture.needsUpdate = true;

			// 创建精灵材质
			const material = new THREE.SpriteMaterial( {
				map: texture,
				transparent: true,
				alphaTest: 0.1
			} );

			// 创建精灵
			const sprite = new THREE.Sprite( material );
			sprite.scale.set( 2.5, 2.5, 1 ); // 进一步增大精灵，让图标更明显

			sprite.name = spotlight.nameI18n?.zhContent || spotlight.name || '亮点';
			sprite.userData = {
				type: 'spotlight',
				spotlightData: spotlight
			};

			// 设置位置
			sprite.position.set(
				Math.random() * 10 - 5,
				Math.random() * 10 - 5,
				Math.random() * 10 - 5
			);

			editor.execute( new AddObjectCommand( editor, sprite ) );

			console.log( '✅ Sidebar亮点精灵已添加到场景:', spotlight );
		} catch ( error ) {
			console.error( '❌ Sidebar添加亮点到场景失败:', error );
		}
	}

	// 创建亮点项
	function createSpotlightItem( spotlight ) {
		const itemRow = new UIRow();
		itemRow.dom.style.padding = '8px';
		itemRow.dom.style.border = '1px solid #eee';
		itemRow.dom.style.marginBottom = '5px';
		itemRow.dom.style.cursor = 'pointer';
		itemRow.dom.style.borderRadius = '3px';
		itemRow.dom.style.display = 'flex';
		itemRow.dom.style.alignItems = 'center';

		// 创建icon容器
		const iconContainer = document.createElement( 'div' );
		iconContainer.style.width = '24px';
		iconContainer.style.height = '24px';
		iconContainer.style.marginRight = '8px';
		iconContainer.style.display = 'flex';
		iconContainer.style.justifyContent = 'center';
		iconContainer.style.alignItems = 'center';
		iconContainer.style.borderRadius = '6px';
		iconContainer.style.backgroundColor = spotlight.iconColor || '#b2c2c2';
		iconContainer.style.flexShrink = '0';

		// 创建SVG图标
		if ( spotlight.iconCode ) {
			const svgIcon = document.createElement( 'svg' );
			svgIcon.className = 'svg-icon';
			svgIcon.style.width = '16px';
			svgIcon.style.height = '16px';
			svgIcon.style.fill = 'white';
			svgIcon.setAttribute( 'aria-hidden', 'true' );
			svgIcon.setAttribute( 'viewBox', '0 0 1024 1024' );

			// 获取图标路径
			const iconPath = getIconPath( spotlight.iconCode );
			if ( iconPath ) {
				const pathElement = document.createElement( 'path' );
				pathElement.setAttribute( 'd', iconPath );
				pathElement.setAttribute( 'fill', 'white' );
				svgIcon.appendChild( pathElement );
			}

			iconContainer.appendChild( svgIcon );
		}

		// 将icon容器添加到itemRow的DOM中
		itemRow.dom.appendChild( iconContainer );

		const nameText = new UIText( spotlight.nameI18n?.zhContent || spotlight.name || '未命名' );
		nameText.setWidth( '100px' );
		nameText.dom.style.fontWeight = 'bold';
		nameText.dom.style.fontSize = '12px';
		nameText.dom.style.overflow = 'hidden';
		nameText.dom.style.textOverflow = 'ellipsis';
		nameText.dom.style.whiteSpace = 'nowrap';

		// 添加鼠标悬浮显示完整名称
		const fullName = spotlight.nameI18n?.zhContent || spotlight.name || '未命名';
		nameText.dom.title = fullName;

		const typeText = new UIText( getTypeLabel( spotlight.dataType ) );
		typeText.setWidth( '50px' );
		typeText.setColor( '#666' );
		typeText.dom.style.fontSize = '11px';

		const addButton = new UIButton( '添加' ).setWidth( '40px' );
		addButton.dom.style.fontSize = '11px';
		addButton.onClick( function () {
			console.log( '添加亮点到场景:', spotlight );
			addSpotlightToScene( spotlight );
		} );

		itemRow.add( nameText );
		itemRow.add( typeText );
		itemRow.add( addButton );

		// 悬停效果
		itemRow.dom.addEventListener( 'mouseenter', function () {
			itemRow.dom.style.backgroundColor = '#f0f0f0';
		} );

		itemRow.dom.addEventListener( 'mouseleave', function () {
			itemRow.dom.style.backgroundColor = '';
		} );

		return itemRow;
	}

	// 渲染亮点列表
	function renderSpotlightList() {
		listContainer.clear();
		
		if ( filteredData.length === 0 ) {
			const emptyText = new UIText( '暂无亮点数据' );
			emptyText.setColor( '#999' );
			emptyText.dom.style.padding = '20px';
			emptyText.dom.style.textAlign = 'center';
			listContainer.add( emptyText );
			return;
		}

		filteredData.forEach( function ( spotlight ) {
			listContainer.add( createSpotlightItem( spotlight ) );
		} );
	}

	// 过滤亮点数据
	function filterSpotlights() {
		const searchTerm = searchInput.getValue().toLowerCase();
		const selectedType = typeSelect.getValue();
		const selectedFactory = factorySelect.getValue();

		filteredData = spotlightData.filter( function ( spotlight ) {
			const nameMatch = !searchTerm ||
				( spotlight.nameI18n?.zhContent || spotlight.name || '' ).toLowerCase().includes( searchTerm );
			const typeMatch = !selectedType || spotlight.dataType == selectedType;
			const factoryMatch = !selectedFactory || spotlight.factoryCode == selectedFactory;

			return nameMatch && typeMatch && factoryMatch;
		} );

		renderSpotlightList();
	}

	// 搜索按钮事件
	searchButton.onClick( function () {
		filterSpotlights();
	} );

	// 重置按钮事件
	resetButton.onClick( function () {
		searchInput.setValue( '' );
		typeSelect.setValue( '' );
		factorySelect.setValue( '' );
		filteredData = spotlightData;
		renderSpotlightList();
	} );

	// 输入框回车搜索
	searchInput.dom.addEventListener( 'keyup', function ( event ) {
		if ( event.key === 'Enter' ) {
			filterSpotlights();
		}
	} );

	// 类型选择变化时自动搜索
	typeSelect.onChange( function () {
		filterSpotlights();
	} );

	// 工厂选择变化时自动搜索
	factorySelect.onChange( function () {
		filterSpotlights();
	} );

	// 设置亮点数据的方法
	function setSpotlightData( data ) {
		console.log( '独立亮点侧边栏收到数据:', data );
		statusText.setValue( `已加载 ${data ? data.length : 0} 个亮点` );
		spotlightData = data || [];
		filteredData = spotlightData;
		renderSpotlightList();
	}

	// 设置工厂数据的方法
	function setFactoryData( data ) {
		console.log( '独立亮点侧边栏收到工厂数据:', data );
		factoryData = data || [];

		// 更新工厂选择器选项
		const factoryOptions = { '': '全部工厂' };
		factoryData.forEach( function ( factory ) {
			factoryOptions[factory.code] = factory.name;
		} );
		factorySelect.setOptions( factoryOptions );
	}

	// 暴露方法给外部调用
	container.setSpotlightData = setSpotlightData;
	container.setFactoryData = setFactoryData;

	return container;

}

export { SidebarSpotlight };
